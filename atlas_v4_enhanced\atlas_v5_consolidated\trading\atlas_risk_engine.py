"""
A.T.L.A.S Risk Core - Consolidated Risk Management Engine
Combines Risk Engine, Portfolio Optimizer, VaR Calculator, and Options Engine
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
import numpy as np

# CRITICAL SECURITY: Import mathematical safeguards
from atlas_math_safeguards import math_safeguards, MathematicalError

# Import models
from models import RiskAssessment

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import get_api_config, settings
from models import EngineStatus, RiskAssessment, Position
from atlas_web_search_service import web_search_service, SearchContext, SearchQuery

logger = logging.getLogger(__name__)


# ============================================================================
# RISK ENGINE
# ============================================================================

class AtlasRiskEngine:
    """Consolidated risk management engine with portfolio optimization"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        
        # Risk parameters
        self.max_position_size = 0.02  # 2% max position size
        self.max_portfolio_risk = 0.20  # 20% max portfolio risk
        self.max_daily_loss = 0.05  # 5% max daily loss
        self.max_correlation = 0.7  # Max correlation between positions
        
        # Portfolio tracking
        self.positions = {}
        self.portfolio_value = 100000.0  # Starting value
        self.daily_pnl = 0.0
        self.risk_metrics = {}

        # Web search service
        self.web_search_service = web_search_service
        self.web_search_enabled = settings.WEB_SEARCH_RISK_ENABLED

        logger.info("[SHIELD] Risk Engine created - safety guardrails active")

    async def initialize(self):
        """Initialize risk engine"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Load risk parameters
            await self._load_risk_parameters()
            
            # Initialize monitoring systems
            await self._initialize_monitoring()

            # Initialize web search service
            if self.web_search_enabled and self.web_search_service.is_available():
                await self.web_search_service.initialize()
                logger.info("[OK] Web search service initialized for risk engine")
            else:
                logger.info("[INFO] Web search disabled or not available for risk engine")

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Risk Engine initialization completed")
            
        except Exception as e:
            logger.error(f"Risk Engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def enhance_risk_assessment_with_web_search(self, symbols: List[str]) -> Dict[str, Any]:
        """
        Enhance risk assessment with web search for regulatory changes, compliance updates, and risk-related news
        """
        try:
            if not self.web_search_enabled or not self.web_search_service.is_available():
                return {"web_search_used": False, "risk_intelligence": {}}

            risk_intelligence = {}

            for symbol in symbols[:3]:  # Limit to 3 symbols to avoid rate limits
                # Search for regulatory and compliance news
                regulatory_results = await self.web_search_service.search_for_context(
                    f"{symbol} SEC regulatory compliance investigation lawsuit",
                    SearchContext.RISK_MANAGEMENT,
                    [symbol],
                    max_results=3
                )

                # Search for risk-related news and events
                risk_results = await self.web_search_service.search_for_context(
                    f"{symbol} risk warning volatility bankruptcy debt crisis",
                    SearchContext.RISK_MANAGEMENT,
                    [symbol],
                    max_results=2
                )

                # Analyze risk indicators from search results
                risk_score = self._calculate_risk_score_from_search(regulatory_results + risk_results)

                risk_intelligence[symbol] = {
                    "regulatory_alerts": [result.__dict__ for result in regulatory_results],
                    "risk_indicators": [result.__dict__ for result in risk_results],
                    "web_risk_score": risk_score,
                    "total_risk_sources": len(regulatory_results) + len(risk_results)
                }

            return {
                "web_search_used": True,
                "symbols_analyzed": len(risk_intelligence),
                "risk_intelligence": risk_intelligence,
                "overall_risk_assessment": self._generate_overall_risk_assessment(risk_intelligence)
            }

        except Exception as e:
            logger.error(f"[RISK_WEB_SEARCH] Enhancement failed: {e}")
            return {"web_search_used": False, "error": str(e)}

    def _calculate_risk_score_from_search(self, search_results: List) -> float:
        """Calculate risk score based on web search results"""
        try:
            if not search_results:
                return 0.0

            risk_score = 0.0
            high_risk_keywords = [
                "investigation", "lawsuit", "sec", "regulatory", "violation",
                "bankruptcy", "debt", "crisis", "warning", "suspension"
            ]

            for result in search_results:
                text = (result.title + " " + result.snippet).lower()
                keyword_matches = sum(1 for keyword in high_risk_keywords if keyword in text)

                # Higher relevance score + more risk keywords = higher risk
                result_risk = (result.relevance_score / 10.0) * (keyword_matches / len(high_risk_keywords))
                risk_score += result_risk

            # Normalize to 0-1 scale
            return min(risk_score / len(search_results), 1.0)

        except Exception as e:
            logger.error(f"[RISK_WEB_SEARCH] Risk score calculation failed: {e}")
            return 0.0

    def _generate_overall_risk_assessment(self, risk_intelligence: Dict[str, Any]) -> str:
        """Generate overall risk assessment summary"""
        try:
            if not risk_intelligence:
                return "No additional risk intelligence found."

            high_risk_symbols = []
            total_sources = 0

            for symbol, data in risk_intelligence.items():
                web_risk_score = data.get("web_risk_score", 0.0)
                sources_count = data.get("total_risk_sources", 0)
                total_sources += sources_count

                if web_risk_score > 0.3:  # High risk threshold
                    high_risk_symbols.append(f"{symbol} (risk: {web_risk_score:.2f})")

            assessment = f"Analyzed {len(risk_intelligence)} symbols with {total_sources} risk sources. "

            if high_risk_symbols:
                assessment += f"HIGH RISK DETECTED: {', '.join(high_risk_symbols)}"
            else:
                assessment += "No significant risk indicators found in web sources."

            return assessment

        except Exception as e:
            logger.error(f"[RISK_WEB_SEARCH] Assessment generation failed: {e}")
            return "Risk assessment completed with web search enhancement."

    async def _load_risk_parameters(self):
        """Load risk management parameters"""
        try:
            # Default risk parameters
            self.risk_parameters = {
                'max_position_size': self.max_position_size,
                'max_portfolio_risk': self.max_portfolio_risk,
                'max_daily_loss': self.max_daily_loss,
                'max_correlation': self.max_correlation,
                'var_confidence': 0.95,  # 95% VaR confidence
                'lookback_days': 252  # 1 year lookback
            }
            
            logger.info(f"[DATA] Risk parameters loaded - Max position size: {self.max_position_size*100}%")
            
        except Exception as e:
            logger.error(f"Failed to load risk parameters: {e}")
            raise

    async def _initialize_monitoring(self):
        """Initialize risk monitoring systems"""
        try:
            # Initialize risk monitoring
            self.monitoring_active = True
            self.alert_thresholds = {
                'position_size': 0.015,  # Alert at 1.5%
                'portfolio_risk': 0.15,  # Alert at 15%
                'daily_loss': 0.03  # Alert at 3%
            }
            
            logger.info("[SEARCH] Risk monitoring systems initialized")
            
        except Exception as e:
            logger.error(f"Risk monitoring initialization failed: {e}")
            raise

    async def assess_position_risk(self, symbol: str, quantity: float, price: float, 
                                 portfolio_value: float = None) -> RiskAssessment:
        """Assess risk for a potential position"""
        try:
            # CRITICAL FIX: Comprehensive input validation
            if not symbol or not isinstance(symbol, str):
                raise ValueError("Invalid symbol parameter")

            if quantity <= 0 or not isinstance(quantity, (int, float)):
                raise ValueError("Invalid quantity parameter - must be positive number")

            if price <= 0 or not isinstance(price, (int, float)):
                raise ValueError("Invalid price parameter - must be positive number")

            if portfolio_value is None:
                portfolio_value = self.portfolio_value

            # CRITICAL FIX: Validate portfolio value
            if portfolio_value <= 0 or not isinstance(portfolio_value, (int, float)):
                logger.error(f"CRITICAL: Invalid portfolio value: {portfolio_value}")
                # Return error assessment with proper model
                return RiskAssessment(
                    symbol=symbol,
                    risk_score=10.0,  # Maximum risk
                    position_size=0.0,
                    stop_loss_price=0.0,
                    risk_amount=0.0,
                    risk_percentage=100.0,  # Maximum risk percentage
                    confidence_level=0.0,  # No confidence
                    risk_factors=["Invalid portfolio value"],
                    recommendations=["Fix portfolio value before trading"],
                    timestamp=datetime.now()
                )

            # Calculate position value with validation
            position_value = quantity * price

            # CRITICAL FIX: Use mathematical safeguards for percentage calculation
            position_size_percent = math_safeguards.safe_percentage(
                position_value,
                portfolio_value,
                default=100.0  # Maximum risk indicator if calculation fails
            )
            
            # Risk assessment
            risk_level = "LOW"
            if position_size_percent > self.max_position_size * 100:
                risk_level = "HIGH"
            elif position_size_percent > self.alert_thresholds['position_size'] * 100:
                risk_level = "MEDIUM"
            
            # Calculate recommended position size
            max_position_value = portfolio_value * self.max_position_size
            recommended_quantity = int(max_position_value / price)
            
            # Risk metrics
            risk_metrics = {
                'position_size_percent': position_size_percent,
                'max_allowed_percent': self.max_position_size * 100,
                'risk_level': risk_level,
                'recommended_quantity': recommended_quantity,
                'max_loss_amount': position_value * 0.02,  # 2% stop loss
                'risk_reward_ratio': 2.0  # Default 2:1
            }
            
            # Calculate risk score (0-10 scale)
            risk_score = 2.0  # Low risk default
            if risk_level == "MEDIUM":
                risk_score = 5.0
            elif risk_level == "HIGH":
                risk_score = 8.0

            # Calculate stop loss price (2% below entry)
            stop_loss_price = price * 0.98

            return RiskAssessment(
                symbol=symbol,
                risk_score=risk_score,
                position_size=position_value,
                stop_loss_price=stop_loss_price,
                risk_amount=risk_metrics['max_loss_amount'],
                risk_percentage=position_size_percent,
                confidence_level=0.8,  # Default confidence
                risk_factors=[f"Position size: {position_size_percent:.1f}%", f"Risk level: {risk_level}"],
                recommendations=[f"Recommended quantity: {recommended_quantity} shares"],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Position risk assessment failed for {symbol}: {e}")
            return RiskAssessment(
                symbol=symbol,
                risk_score=10.0,  # Maximum risk
                position_size=0.0,
                stop_loss_price=0.0,
                risk_amount=0.0,
                risk_percentage=100.0,  # Maximum risk percentage
                confidence_level=0.0,  # No confidence
                risk_factors=["Assessment failed", str(e)],
                recommendations=["Review input parameters"],
                timestamp=datetime.now()
            )

    async def calculate_portfolio_risk(self, positions: Dict[str, Position] = None) -> Dict[str, Any]:
        """Calculate overall portfolio risk metrics"""
        try:
            if positions is None:
                positions = self.positions
            
            if not positions:
                return {
                    'total_risk': 0.0,
                    'var_95': 0.0,
                    'max_drawdown': 0.0,
                    'concentration_risk': 0.0,
                    'correlation_risk': 0.0
                }
            
            # Calculate basic risk metrics
            total_value = sum(pos.market_value for pos in positions.values())
            largest_position = max(pos.market_value for pos in positions.values()) if positions else 0
            concentration_risk = (largest_position / total_value) * 100 if total_value > 0 else 0
            
            # Simplified VaR calculation (normally would use historical data)
            portfolio_volatility = 0.15  # Assume 15% annual volatility
            var_95 = total_value * portfolio_volatility * 2.33 / np.sqrt(252)  # Daily 95% VaR
            
            return {
                'total_value': total_value,
                'total_risk': min(concentration_risk, 100.0),
                'var_95': var_95,
                'max_drawdown': 0.0,  # Would calculate from historical data
                'concentration_risk': concentration_risk,
                'correlation_risk': 0.0,  # Would calculate from correlation matrix
                'position_count': len(positions),
                'risk_level': 'LOW' if concentration_risk < 10 else 'MEDIUM' if concentration_risk < 20 else 'HIGH'
            }
            
        except Exception as e:
            logger.error(f"Portfolio risk calculation failed: {e}")
            return {'error': str(e)}

    async def check_risk_limits(self, symbol: str, quantity: float, price: float) -> Dict[str, Any]:
        """Check if trade violates risk limits"""
        try:
            # Assess position risk
            risk_assessment = await self.assess_position_risk(symbol, quantity, price)
            
            # Check limits
            violations = []
            warnings = []
            
            if risk_assessment.position_size_percent > self.max_position_size * 100:
                violations.append(f"Position size {risk_assessment.position_size_percent:.1f}% exceeds limit {self.max_position_size*100}%")
            elif risk_assessment.position_size_percent > self.alert_thresholds['position_size'] * 100:
                warnings.append(f"Position size {risk_assessment.position_size_percent:.1f}% approaching limit")
            
            # Check daily loss limit
            if abs(self.daily_pnl) > self.max_daily_loss * self.portfolio_value:
                violations.append(f"Daily loss limit exceeded")
            
            return {
                'approved': len(violations) == 0,
                'violations': violations,
                'warnings': warnings,
                'risk_assessment': risk_assessment,
                'recommended_action': 'APPROVE' if len(violations) == 0 else 'REJECT'
            }
            
        except Exception as e:
            logger.error(f"Risk limit check failed: {e}")
            return {
                'approved': False,
                'violations': [f"Risk check error: {str(e)}"],
                'warnings': [],
                'recommended_action': 'REJECT'
            }

    async def optimize_position_size(self, symbol: str, target_price: float,
                                   stop_loss: float, risk_amount: float = None) -> Dict[str, Any]:
        """Optimize position size based on risk parameters"""
        try:
            # CRITICAL FIX: Comprehensive input validation
            if not symbol or not isinstance(symbol, str):
                return {'error': 'Invalid symbol parameter'}

            if target_price <= 0 or not isinstance(target_price, (int, float)):
                return {'error': 'Invalid target price - must be positive number'}

            if stop_loss <= 0 or not isinstance(stop_loss, (int, float)):
                return {'error': 'Invalid stop loss - must be positive number'}

            if risk_amount is None:
                if self.portfolio_value <= 0:
                    return {'error': 'Invalid portfolio value - cannot calculate risk amount'}
                risk_amount = self.portfolio_value * self.max_position_size

            if risk_amount <= 0:
                return {'error': 'Invalid risk amount - must be positive'}

            # CRITICAL FIX: Calculate risk per share with validation
            risk_per_share = abs(target_price - stop_loss)

            # CRITICAL FIX: Prevent division by zero and validate risk per share
            if risk_per_share <= 0.01:  # Minimum 1 cent risk per share
                return {
                    'recommended_shares': 0,
                    'position_value': 0,
                    'risk_amount': 0,
                    'error': 'Stop loss too close to target price - minimum 1 cent difference required'
                }

            # CRITICAL FIX: Use mathematical safeguards for position size calculation
            recommended_shares = int(math_safeguards.safe_divide(
                risk_amount,
                risk_per_share,
                default=0
            ))

            if recommended_shares <= 0:
                return {
                    'recommended_shares': 0,
                    'position_value': 0,
                    'risk_amount': risk_amount,
                    'error': 'Calculated position size is zero - risk amount too small'
                }

            # CRITICAL FIX: Use mathematical safeguards for position value calculation
            position_value = math_safeguards.safe_multiply(
                recommended_shares,
                target_price,
                default=0
            )

            # CRITICAL FIX: Use mathematical safeguards for percentage calculation
            position_size_percent = math_safeguards.safe_percentage(
                position_value,
                self.portfolio_value,
                default=100.0  # Maximum risk indicator
            )
            
            return {
                'recommended_shares': recommended_shares,
                'position_value': position_value,
                'position_size_percent': position_size_percent,
                'risk_amount': risk_amount,
                'risk_per_share': risk_per_share,
                'max_loss': recommended_shares * risk_per_share
            }
            
        except Exception as e:
            logger.error(f"Position size optimization failed: {e}")
            return {'error': str(e)}

    def get_risk_summary(self) -> Dict[str, Any]:
        """Get risk management summary"""
        return {
            'status': self.status.value,
            'risk_parameters': self.risk_parameters,
            'portfolio_value': self.portfolio_value,
            'daily_pnl': self.daily_pnl,
            'position_count': len(self.positions),
            'monitoring_active': getattr(self, 'monitoring_active', False),
            'max_position_size_percent': self.max_position_size * 100,
            'max_portfolio_risk_percent': self.max_portfolio_risk * 100
        }


# ============================================================================
# PORTFOLIO OPTIMIZER
# ============================================================================

class AtlasPortfolioOptimizer:
    """Portfolio optimization engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        logger.info("[UP] Portfolio Optimizer initialized - enabled: True")

    async def initialize(self):
        """Initialize portfolio optimizer"""
        self.status = EngineStatus.ACTIVE
        logger.info("[OK] Portfolio Optimizer ready")

    async def optimize_portfolio(self, symbols: List[str], target_return: float = 0.10) -> Dict[str, Any]:
        """Optimize portfolio allocation"""
        try:
            # Simplified optimization - equal weight for now
            num_symbols = len(symbols)
            if num_symbols == 0:
                return {'error': 'No symbols provided'}
            
            weight_per_symbol = 1.0 / num_symbols
            
            allocation = {}
            for symbol in symbols:
                allocation[symbol] = {
                    'weight': weight_per_symbol,
                    'weight_percent': weight_per_symbol * 100
                }
            
            return {
                'allocation': allocation,
                'expected_return': target_return,
                'expected_risk': 0.15,  # Simplified
                'sharpe_ratio': target_return / 0.15,
                'optimization_method': 'equal_weight'
            }
            
        except Exception as e:
            logger.error(f"Portfolio optimization failed: {e}")
            return {'error': str(e)}


# ============================================================================
# VAR CALCULATOR
# ============================================================================

class AtlasVaRCalculator:
    """Value at Risk calculator"""
    
    def __init__(self):
        self.confidence_levels = [0.95, 0.99]
        logger.info("[CALC] VaR Calculator initialized")

    async def calculate_var(self, portfolio_value: float, volatility: float = 0.15, 
                          confidence: float = 0.95, time_horizon: int = 1) -> Dict[str, Any]:
        """Calculate Value at Risk"""
        try:
            # Simplified VaR calculation using normal distribution
            from scipy.stats import norm
            
            # Calculate VaR
            z_score = norm.ppf(confidence)
            daily_volatility = volatility / np.sqrt(252)
            var_amount = portfolio_value * daily_volatility * z_score * np.sqrt(time_horizon)
            
            return {
                'var_amount': var_amount,
                'var_percent': (var_amount / portfolio_value) * 100,
                'confidence': confidence,
                'time_horizon_days': time_horizon,
                'portfolio_value': portfolio_value,
                'volatility': volatility
            }
            
        except Exception as e:
            logger.error(f"VaR calculation failed: {e}")
            # Fallback calculation
            var_amount = portfolio_value * 0.02  # 2% simple estimate
            return {
                'var_amount': var_amount,
                'var_percent': 2.0,
                'confidence': confidence,
                'time_horizon_days': time_horizon,
                'portfolio_value': portfolio_value,
                'method': 'simplified'
            }


# ============================================================================
# OPTIONS ENGINE
# ============================================================================

class AtlasOptionsEngine:
    """Options trading and analysis engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        logger.info("[TARGET] Options Engine initialized - enabled: True")

    async def initialize(self):
        """Initialize options engine"""
        self.status = EngineStatus.ACTIVE
        logger.info("[OK] Options Engine ready")

    async def calculate_option_price(self, underlying_price: float, strike_price: float,
                                   time_to_expiry: float, volatility: float = 0.25,
                                   risk_free_rate: float = 0.05, option_type: str = 'call') -> Dict[str, Any]:
        """Calculate option price using Black-Scholes"""
        try:
            # Simplified Black-Scholes calculation
            import math
            
            d1 = (math.log(underlying_price / strike_price) + 
                  (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * math.sqrt(time_to_expiry))
            d2 = d1 - volatility * math.sqrt(time_to_expiry)
            
            # Approximate normal CDF
            def norm_cdf(x):
                return 0.5 * (1 + math.erf(x / math.sqrt(2)))
            
            if option_type.lower() == 'call':
                price = (underlying_price * norm_cdf(d1) - 
                        strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm_cdf(d2))
            else:  # put
                price = (strike_price * math.exp(-risk_free_rate * time_to_expiry) * norm_cdf(-d2) - 
                        underlying_price * norm_cdf(-d1))
            
            return {
                'option_price': max(0, price),
                'delta': norm_cdf(d1) if option_type.lower() == 'call' else norm_cdf(d1) - 1,
                'gamma': math.exp(-d1**2/2) / (underlying_price * volatility * math.sqrt(2 * math.pi * time_to_expiry)),
                'theta': -0.01,  # Simplified
                'vega': underlying_price * math.sqrt(time_to_expiry) * math.exp(-d1**2/2) / math.sqrt(2 * math.pi),
                'rho': 0.01  # Simplified
            }
            
        except Exception as e:
            logger.error(f"Option price calculation failed: {e}")
            return {
                'option_price': 0.0,
                'error': str(e)
            }


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasRiskEngine",
    "AtlasPortfolioOptimizer",
    "AtlasVaRCalculator", 
    "AtlasOptionsEngine"
]
