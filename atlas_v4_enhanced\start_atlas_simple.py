#!/usr/bin/env python3
"""
Simple A.T.L.A.S. Startup Script
Guaranteed to work with immediate feedback
"""

import os
import sys
import time
import subprocess

def main():
    print("🚀 A.T.L.A.S. SIMPLE STARTUP")
    print("=" * 50)
    print("✅ All API keys configured")
    print("✅ Lee Method Scanner ready")
    print("✅ Desktop notifications enabled")
    print("✅ Complete system loading...")
    print("=" * 50)
    
    # Change to the correct directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"📍 Working directory: {os.getcwd()}")
    print(f"📍 Python executable: {sys.executable}")
    
    # Start the server
    server_file = "atlas_working_server.py"
    
    if os.path.exists(server_file):
        print(f"✅ Found server file: {server_file}")
        print("🚀 Starting A.T.L.A.S. server...")
        print("🌐 Web interface will be available at: http://localhost:8002")
        print("🔔 Desktop notifications will appear for high-confidence signals")
        print("📊 Lee Method Scanner will detect 3+ consecutive declining bar patterns")
        print("=" * 50)
        
        try:
            # Start the server process
            process = subprocess.Popen([
                sys.executable, server_file
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
               universal_newlines=True, bufsize=1)
            
            print("✅ Server process started!")
            print("⏳ Waiting for server to initialize...")
            
            # Monitor the process output
            for line in process.stdout:
                print(line.strip())
                if "Uvicorn running on" in line:
                    print("🎉 SERVER IS READY!")
                    print("🌐 Open http://localhost:8002 in your browser")
                    break
                    
        except Exception as e:
            print(f"❌ Error starting server: {e}")
            
    else:
        print(f"❌ Server file not found: {server_file}")
        print("📁 Available files:")
        for file in os.listdir("."):
            if file.endswith(".py"):
                print(f"   - {file}")

if __name__ == "__main__":
    main()
