#!/usr/bin/env python3
"""
Test desktop notification system for Lee Method Scanner
"""

import asyncio
import sys
sys.path.append('.')

async def test_notification():
    from atlas_lee_method import LeeMethodSignal
    from datetime import datetime
    
    # Create a test signal similar to PLTR
    test_signal = LeeMethodSignal(
        symbol='TEST',
        signal_type='active_decline_reversal_opportunity',
        entry_price=148.06,
        target_price=155.00,
        stop_loss=140.00,
        confidence=0.75,
        timeframe='1day',
        timestamp=datetime.now(),
        histogram_sequence=[],
        momentum_bars=[],
        momentum_confirmation=True,
        current_price=148.06,
        pattern_description='Active decline: 3 consecutive declining bars (-3.85% decline) - Reversal opportunity'
    )
    
    # Test the notification method
    from atlas_lee_method import AtlasLeeMethodRealtimeScanner
    from config import get_api_config
    
    fmp_config = get_api_config('fmp')
    fmp_api_key = fmp_config.get('api_key') if fmp_config else None
    
    scanner = AtlasLeeMethodRealtimeScanner(fmp_api_key)
    
    print('Testing desktop notification...')
    await scanner._send_desktop_notification(test_signal)
    print('Desktop notification test completed!')

if __name__ == "__main__":
    asyncio.run(test_notification())
