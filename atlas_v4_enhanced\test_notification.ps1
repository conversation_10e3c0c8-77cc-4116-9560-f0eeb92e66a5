Add-Type -AssemblyName System.Windows.Forms
$notification = New-Object System.Windows.Forms.NotifyIcon
$notification.Icon = [System.Drawing.SystemIcons]::Information
$notification.BalloonTipTitle = "🚨 LEE METHOD ALERT TEST"
$notification.BalloonTipText = "Desktop notifications are working! You will be alerted when 3+ consecutive red bar patterns are found."
$notification.Visible = $true
$notification.ShowBalloonTip(5000)
Start-Sleep -Seconds 6
$notification.Dispose()
Write-Host "✅ Desktop notification test completed!"
