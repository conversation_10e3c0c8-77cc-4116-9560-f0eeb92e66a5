#!/usr/bin/env python3
"""
Test the complete Lee Method system end-to-end:
1. Orchestrator initialization with realtime scanner
2. Signal detection and storage
3. Desktop notifications
4. API endpoint simulation
5. Web interface data flow
"""

import asyncio
import sys
import json
from datetime import datetime
sys.path.append('.')

async def test_complete_system():
    print("🚀 TESTING COMPLETE LEE METHOD SYSTEM")
    print("=" * 60)
    
    try:
        # Step 1: Initialize the complete system (like the server does)
        print("1️⃣ INITIALIZING SYSTEM COMPONENTS...")
        
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()
        
        print("✅ Orchestrator initialized with realtime Lee Method scanner")
        
        # Step 2: Get the Lee Method engine and verify it's the realtime scanner
        lee_engine = orchestrator.engines.get('lee_method')
        if not lee_engine:
            print("❌ Lee Method engine not found")
            return
            
        print(f"✅ Lee Method engine: {type(lee_engine).__name__}")
        print(f"   Has active_signals: {hasattr(lee_engine, 'active_signals')}")
        print(f"   Scanner running: {getattr(lee_engine, 'is_running', False)}")
        
        # Step 3: Simulate signal detection by manually scanning some symbols
        print(f"\n2️⃣ SIMULATING SIGNAL DETECTION...")
        
        # Use the main scanner to detect signals and store them in the realtime scanner
        from atlas_lee_method import LeeMethodScanner
        from config import get_api_config
        
        fmp_config = get_api_config('fmp')
        fmp_api_key = fmp_config.get('api_key') if fmp_config else None
        
        main_scanner = LeeMethodScanner(fmp_api_key, orchestrator.engines.get('market'))
        
        # Test symbols that are likely to have signals
        test_symbols = ['PLTR', 'NVDA', 'TSLA', 'SPY', 'QQQ']
        signals_detected = 0
        high_confidence_signals = 0
        
        for symbol in test_symbols:
            try:
                signal = await main_scanner.scan_symbol(symbol)
                if signal:
                    signals_detected += 1
                    
                    # Store in the realtime scanner's active_signals
                    lee_engine.active_signals[symbol] = signal
                    
                    print(f"✅ {symbol}: {signal.confidence:.1%} confidence - {signal.description[:50]}...")
                    
                    # Test desktop notification for high-confidence signals
                    if signal.confidence >= 0.75:
                        high_confidence_signals += 1
                        print(f"   🔔 Desktop notification triggered for {symbol}")
                        await lee_engine._send_desktop_notification(signal)
                else:
                    print(f"❌ {symbol}: No signal detected")
                    
            except Exception as e:
                print(f"❌ {symbol}: Error - {e}")
        
        print(f"\n📊 DETECTION SUMMARY:")
        print(f"   Total signals detected: {signals_detected}")
        print(f"   High-confidence signals: {high_confidence_signals}")
        print(f"   Signals in active storage: {len(lee_engine.active_signals)}")
        
        # Step 4: Test orchestrator signal retrieval (API backend)
        print(f"\n3️⃣ TESTING API BACKEND (Orchestrator)...")
        
        signals_data = await orchestrator.get_lee_method_signals()
        api_signals = signals_data.get('signals', [])
        
        print(f"✅ Orchestrator retrieved {len(api_signals)} signals")
        
        # Update cache (like the server does)
        orchestrator._cached_lee_signals = api_signals
        orchestrator._lee_cache_timestamp = datetime.now().timestamp()
        
        # Step 5: Simulate API endpoint responses
        print(f"\n4️⃣ SIMULATING API ENDPOINTS...")
        
        # Simulate /api/v1/lee_method/signals endpoint
        regular_api_response = {
            "success": True,
            "signals": api_signals,
            "cached": True,
            "cache_age": 0.0
        }
        
        # Simulate /api/v1/lee_method/signals/active endpoint (high-confidence only)
        active_signals = []
        for signal in api_signals:
            confidence = signal.get('confidence', 0)
            description = signal.get('description', '')
            
            if confidence >= 0.75 or '3 consecutive' in description or 'Active decline' in description:
                active_signals.append(signal)
        
        fast_api_response = {
            "success": True,
            "signals": active_signals[:10],
            "total_active": len(active_signals),
            "response_time": "real-time",
            "cached": False
        }
        
        print(f"✅ Regular API endpoint: {len(regular_api_response['signals'])} signals")
        print(f"✅ Fast API endpoint: {len(fast_api_response['signals'])} high-confidence signals")
        
        # Step 6: Display results for web interface
        print(f"\n5️⃣ WEB INTERFACE DATA PREVIEW...")
        
        if active_signals:
            print("🎯 HIGH-CONFIDENCE SIGNALS FOR WEB INTERFACE:")
            for i, signal in enumerate(active_signals[:3]):
                symbol = signal.get('symbol', 'UNKNOWN')
                confidence = signal.get('confidence', 0)
                description = signal.get('description', 'No description')
                price = signal.get('price', 0)
                
                print(f"   {i+1}. {symbol}")
                print(f"      Confidence: {confidence:.1%}")
                print(f"      Price: ${price:.2f}")
                print(f"      Description: {description}")
                print()
        else:
            print("❌ No high-confidence signals for web interface")
        
        # Step 7: System health check
        print(f"6️⃣ SYSTEM HEALTH CHECK...")
        
        health_status = {
            "orchestrator_initialized": True,
            "lee_method_engine_type": type(lee_engine).__name__,
            "realtime_scanner_active": getattr(lee_engine, 'is_running', False),
            "active_signals_count": len(lee_engine.active_signals),
            "cached_signals_available": hasattr(orchestrator, '_cached_lee_signals'),
            "desktop_notifications_working": high_confidence_signals > 0,
            "api_endpoints_ready": len(api_signals) >= 0
        }
        
        print("✅ SYSTEM HEALTH STATUS:")
        for key, value in health_status.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {value}")
        
        print(f"\n🎉 COMPLETE SYSTEM TEST FINISHED!")
        print(f"   The Lee Method Scanner is now properly integrated and working!")
        print(f"   Desktop notifications: {'✅ Active' if high_confidence_signals > 0 else '❌ No signals to test'}")
        print(f"   Web interface ready: {'✅ Yes' if len(active_signals) > 0 else '⚠️  Waiting for signals'}")
        
    except Exception as e:
        print(f"❌ Complete system test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_complete_system())
