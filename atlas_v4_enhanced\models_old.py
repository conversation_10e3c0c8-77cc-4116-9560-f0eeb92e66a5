"""
A.T.L.A.S Models - Compatibility Bridge
This file provides backward compatibility by importing from the consolidated models.
"""

import sys
import os
from datetime import datetime
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from enum import Enum

# Add consolidated path to imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'core'))

# Try to import consolidated models, but always define fallbacks
CONSOLIDATED_MODELS_AVAILABLE = False
try:
    from models import *
    CONSOLIDATED_MODELS_AVAILABLE = True
except ImportError:
    pass

# Always define fallback models to ensure availability
class EngineStatus(Enum):
    """Engine status enumeration"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    STOPPED = "stopped"
    FAILED = "failed"
    MAINTENANCE = "maintenance"

class SignalStrength(Enum):
    """Signal strength enumeration"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"

class AlertType(Enum):
    """Alert type enumeration"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertPriority(Enum):
    """Alert priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    BRACKET = "bracket"
    TRAILING_STOP = "trailing_stop"

class OrderStatus(Enum):
    """Order status enumeration"""
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"

@dataclass
class Quote:
    """Stock quote data structure"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None
    previous_close: Optional[float] = None

@dataclass
class Order:
    """Trading order data structure"""
    symbol: str
    quantity: float
    side: OrderSide
    type: OrderType
    timestamp: datetime
    id: Optional[str] = None
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: str = "NEW"
    filled_quantity: float = 0.0

    class OrderSide(str, Enum):
        BUY = "buy"
        SELL = "sell"

    class OrderType(str, Enum):
        MARKET = "market"
        LIMIT = "limit"
        STOP = "stop"
        STOP_LIMIT = "stop_limit"
        BRACKET = "bracket"
        TRAILING_STOP = "trailing_stop"

    class OrderStatus(str, Enum):
        NEW = "new"
        PARTIALLY_FILLED = "partially_filled"
        FILLED = "filled"
        CANCELED = "canceled"
        REJECTED = "rejected"
    
    class EngineStatus(Enum):
        """Engine status enumeration"""
        INITIALIZING = "initializing"
        ACTIVE = "active"
        STOPPED = "stopped"
        FAILED = "failed"
        MAINTENANCE = "maintenance"
    
    class SignalStrength(Enum):
        """Signal strength enumeration"""
        WEAK = "weak"
        MODERATE = "moderate"
        STRONG = "strong"
        VERY_STRONG = "very_strong"
    
    class AlertType(Enum):
        """Alert type enumeration"""
        INFO = "info"
        WARNING = "warning"
        ERROR = "error"
        CRITICAL = "critical"
    
    class AlertPriority(Enum):
        """Alert priority enumeration"""
        LOW = "low"
        MEDIUM = "medium"
        HIGH = "high"
        URGENT = "urgent"
    
    @dataclass
    class Quote:
        """Stock quote data structure"""
        symbol: str
        price: float
        change: float
        change_percent: float
        volume: int
        timestamp: datetime
        bid: Optional[float] = None
        ask: Optional[float] = None
        high: Optional[float] = None
        low: Optional[float] = None
        open: Optional[float] = None
        previous_close: Optional[float] = None

    @dataclass
    class Order:
        """Trading order data structure"""
        symbol: str
        quantity: float
        side: OrderSide
        type: OrderType
        timestamp: datetime
        id: Optional[str] = None
        price: Optional[float] = None
        stop_price: Optional[float] = None
        status: str = "NEW"
        filled_quantity: float = 0.0
    
    @dataclass
    class TTMSqueezeSignal:
        """TTM Squeeze signal data structure"""
        symbol: str
        signal_type: str
        strength: SignalStrength
        confidence: float
        price: float
        timestamp: datetime
        criteria_met: List[str]
        technical_indicators: Dict[str, float]
        
    @dataclass
    class LeeMethodSignal:
        """Lee Method signal data structure"""
        symbol: str
        pattern_type: str
        confidence: float
        entry_price: float
        target_price: float
        stop_loss: float
        timestamp: datetime
        criteria_scores: Dict[str, float]
        
    @dataclass
    class TradingSignal:
        """General trading signal data structure"""
        symbol: str
        signal_type: str
        action: str  # BUY, SELL, HOLD
        confidence: float
        price: float
        timestamp: datetime
        source: str
        metadata: Dict[str, Any]
        
    @dataclass
    class Position:
        """Trading position data structure"""
        symbol: str
        quantity: float
        average_price: float
        current_price: float
        market_value: float
        unrealized_pnl: float
        realized_pnl: float
        timestamp: datetime
        
    @dataclass
    class Trade:
        """Trade execution data structure"""
        symbol: str
        action: str
        quantity: float
        price: float
        timestamp: datetime
        trade_id: str
        status: str
        commission: float = 0.0
        
    @dataclass
    class PortfolioSummary:
        """Portfolio summary data structure"""
        total_value: float
        cash: float
        buying_power: float
        day_pnl: float
        total_pnl: float
        positions_count: int
        timestamp: datetime
        
    @dataclass
    class MarketData:
        """Market data container"""
        symbol: str
        quotes: List[Any]
        historical_data: Optional[Dict[str, Any]] = None
        real_time_data: Optional[Dict[str, Any]] = None
        
    @dataclass
    class ScanResult:
        """Scanner result data structure"""
        symbol: str
        pattern_found: bool
        pattern_type: str
        confidence: float
        signal_strength: SignalStrength
        price: float
        volume: int
        timestamp: datetime
        
    @dataclass
    class AlertSignal:
        """Alert signal data structure"""
        alert_id: str
        symbol: str
        alert_type: AlertType
        priority: AlertPriority
        message: str
        timestamp: datetime
        metadata: Dict[str, Any]
        
    @dataclass
    class RiskMetrics:
        """Risk metrics data structure"""
        symbol: str
        var_95: float  # Value at Risk 95%
        volatility: float
        beta: float
        sharpe_ratio: float
        max_drawdown: float
        timestamp: datetime
        
    @dataclass
    class PerformanceMetrics:
        """Performance metrics data structure"""
        total_return: float
        annualized_return: float
        volatility: float
        sharpe_ratio: float
        max_drawdown: float
        win_rate: float
        profit_factor: float
        timestamp: datetime
        
    @dataclass
    class MLPredictionResult:
        """Machine learning prediction result"""
        symbol: str
        prediction_type: str
        predicted_value: float
        confidence: float
        model_used: str
        features_used: List[str]
        timestamp: datetime
        
    @dataclass
    class NewsItem:
        """News item data structure"""
        title: str
        content: str
        source: str
        url: str
        timestamp: datetime
        sentiment_score: Optional[float] = None
        relevance_score: Optional[float] = None
        
    @dataclass
    class SentimentData:
        """Sentiment analysis data structure"""
        symbol: str
        sentiment_score: float
        sentiment_label: str  # POSITIVE, NEGATIVE, NEUTRAL
        confidence: float
        source: str
        timestamp: datetime
        
    class SystemHealth:
        """System health monitoring"""
        def __init__(self):
            self.status = EngineStatus.ACTIVE
            self.components = {}
            self.last_check = datetime.now()
            
        def update_component(self, component: str, status: EngineStatus):
            self.components[component] = {
                "status": status,
                "last_update": datetime.now()
            }
            
        def get_overall_status(self) -> EngineStatus:
            if not self.components:
                return EngineStatus.INITIALIZING
                
            statuses = [comp["status"] for comp in self.components.values()]
            
            if any(status == EngineStatus.FAILED for status in statuses):
                return EngineStatus.FAILED
            elif any(status == EngineStatus.MAINTENANCE for status in statuses):
                return EngineStatus.MAINTENANCE
            elif all(status == EngineStatus.ACTIVE for status in statuses):
                return EngineStatus.ACTIVE
            else:
                return EngineStatus.INITIALIZING

# Export all models for compatibility
__all__ = [
    'EngineStatus', 'SignalStrength', 'AlertType', 'AlertPriority',
    'OrderSide', 'OrderType', 'OrderStatus',
    'Quote', 'Order', 'Position', 'Trade',
    'TTMSqueezeSignal', 'LeeMethodSignal', 'TradingSignal',
    'PortfolioSummary', 'MarketData', 'ScanResult',
    'AlertSignal', 'RiskMetrics', 'PerformanceMetrics', 'MLPredictionResult',
    'NewsItem', 'SentimentData', 'SystemHealth'
]
