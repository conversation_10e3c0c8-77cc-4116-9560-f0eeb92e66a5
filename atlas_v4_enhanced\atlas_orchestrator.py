"""
A.T.L.A.S Orchestrator - Main System Orchestrator
Coordinates all A.T.L.A.S. engines and components
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from config import settings
from models import EngineStatus
from atlas_web_search_service import web_search_service, SearchContext, SearchQuery

# Import all consolidated engines
from atlas_ai_core import AtlasAIEngine
from atlas_trading_core import AtlasTradingEngine
from atlas_market_core import AtlasMarketEngine
from atlas_risk_core import AtlasRiskEngine
from atlas_education import AtlasEducationEngine
from atlas_database import AtlasDatabaseManager
from atlas_lee_method import LeeMethodScanner
from atlas_utils import AtlasUtilsOrchestrator
from atlas_trading_plan_engine import AtlasTradingPlanEngine

# Options Engine (with graceful fallback)
try:
    from atlas_options import AtlasOptionsOrchestrator
    OPTIONS_AVAILABLE = True
except ImportError:
    OPTIONS_AVAILABLE = False

# News Insights Engine (with graceful fallback)
try:
    from atlas_news_insights_engine import AtlasNewsInsightsOrchestrator
    NEWS_INSIGHTS_AVAILABLE = True
except ImportError:
    NEWS_INSIGHTS_AVAILABLE = False

# Grok integration monitoring (with graceful fallback)
try:
    from atlas_grok_integration import AtlasGrokIntegrationEngine
    GROK_INTEGRATION_AVAILABLE = True
except ImportError:
    GROK_INTEGRATION_AVAILABLE = False

logger = logging.getLogger(__name__)

# ============================================================================
# MAIN ORCHESTRATOR
# ============================================================================

class AtlasOrchestrator:
    """Main system orchestrator for A.T.L.A.S."""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.engines = {}
        self.initialization_order = [
            'database',
            'utils',
            'market',
            'risk',
            'trading',
            'trading_plan',  # Comprehensive trading plan engine
            'options',  # Add options engine for comprehensive options strategy support
            'education',
            'news_insights',  # News Insights Engine for real-time market news analysis
            'ai',
            'lee_method'
        ]

        # News Insights monitoring
        self.news_insights_available = NEWS_INSIGHTS_AVAILABLE

        # Options engine monitoring
        self.options_available = OPTIONS_AVAILABLE

        # Grok integration monitoring
        self.grok_integration_available = GROK_INTEGRATION_AVAILABLE
        self.grok_status_cache = {}
        self.grok_performance_metrics = {
            'total_enhancements': 0,
            'successful_enhancements': 0,
            'average_improvement': 0.0,
            'last_update': None
        }
        
        logger.info(f"[ORCHESTRATOR] A.T.L.A.S. Orchestrator initialized - Grok: {self.grok_integration_available}")

    async def initialize(self):
        """Initialize all A.T.L.A.S. engines in proper order"""
        try:
            self.status = EngineStatus.INITIALIZING
            logger.info("[INIT] Starting A.T.L.A.S. system initialization...")
            
            # Initialize engines in order
            for engine_name in self.initialization_order:
                await self._initialize_engine(engine_name)
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] A.T.L.A.S. system initialization completed successfully")
            
        except Exception as e:
            logger.error(f"A.T.L.A.S. initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_engine(self, engine_name: str):
        """Initialize individual engine"""
        try:
            logger.info(f"[INIT] Initializing {engine_name} engine...")
            
            if engine_name == 'database':
                self.engines['database'] = AtlasDatabaseManager()
                await self.engines['database'].initialize()
                
            elif engine_name == 'utils':
                self.engines['utils'] = AtlasUtilsOrchestrator()
                await self.engines['utils'].initialize()
                
            elif engine_name == 'market':
                self.engines['market'] = AtlasMarketEngine()
                await self.engines['market'].initialize()
                
            elif engine_name == 'risk':
                self.engines['risk'] = AtlasRiskEngine()
                await self.engines['risk'].initialize()
                
            elif engine_name == 'trading':
                self.engines['trading'] = AtlasTradingEngine()
                await self.engines['trading'].initialize()

            elif engine_name == 'trading_plan':
                self.engines['trading_plan'] = AtlasTradingPlanEngine()
                await self.engines['trading_plan'].initialize()

            elif engine_name == 'options':
                # Initialize Options Engine with graceful fallback
                if OPTIONS_AVAILABLE:
                    self.engines['options'] = AtlasOptionsOrchestrator()
                    await self.engines['options'].initialize()
                    logger.info("[OK] Options Engine initialized successfully")
                else:
                    logger.warning("[WARN] Options Engine not available - continuing without options analysis")

            elif engine_name == 'education':
                self.engines['education'] = AtlasEducationEngine()
                await self.engines['education'].initialize()

            elif engine_name == 'news_insights':
                # Initialize News Insights Engine with graceful fallback
                if NEWS_INSIGHTS_AVAILABLE:
                    self.engines['news_insights'] = AtlasNewsInsightsOrchestrator()
                    await self.engines['news_insights'].initialize()
                    logger.info("[OK] News Insights Engine initialized successfully")
                else:
                    logger.warning("[WARN] News Insights Engine not available - continuing without news analysis")

            elif engine_name == 'ai':
                self.engines['ai'] = AtlasAIEngine()
                await self.engines['ai'].initialize()

            elif engine_name == 'lee_method':
                # ✅ FIXED: Initialize the realtime Lee Method scanner that actually stores signals
                from config import get_api_config
                fmp_config = get_api_config('fmp')
                fmp_api_key = fmp_config.get('api_key') if fmp_config else None

                # Use the global realtime scanner instance that's actually detecting signals
                # This is the same scanner that's created in atlas_server.py
                try:
                    # Try to get the global realtime scanner from the server
                    import atlas_server
                    if hasattr(atlas_server, 'realtime_scanner') and atlas_server.realtime_scanner:
                        self.engines['lee_method'] = atlas_server.realtime_scanner
                        logger.info("[OK] Using global realtime scanner instance for Lee Method signals")
                    else:
                        # Fallback to creating our own scanner
                        from atlas_lee_method import get_scanner_instance
                        self.engines['lee_method'] = get_scanner_instance(fmp_api_key)
                        logger.info("[FALLBACK] Created new Lee Method scanner instance")

                        if hasattr(self.engines['lee_method'], 'initialize'):
                            await self.engines['lee_method'].initialize()

                        # Start the scanning process to populate active_signals
                        if hasattr(self.engines['lee_method'], 'start_scanner'):
                            await self.engines['lee_method'].start_scanner()
                            logger.info("[OK] Lee Method realtime scanner started and will populate active_signals")
                        elif hasattr(self.engines['lee_method'], 'start_scanning'):
                            await self.engines['lee_method'].start_scanning()
                            logger.info("[OK] Lee Method realtime scanner started and will populate active_signals")
                except Exception as e:
                    logger.warning(f"[FALLBACK] Could not access global realtime scanner: {e}")
                    # Fallback to creating our own scanner
                    from atlas_lee_method import get_scanner_instance
                    self.engines['lee_method'] = get_scanner_instance(fmp_api_key)
                    logger.info("[FALLBACK] Created new Lee Method scanner instance")

                    if hasattr(self.engines['lee_method'], 'initialize'):
                        await self.engines['lee_method'].initialize()

                    # Start the scanning process to populate active_signals
                    if hasattr(self.engines['lee_method'], 'start_scanner'):
                        await self.engines['lee_method'].start_scanner()
                        logger.info("[OK] Lee Method realtime scanner started and will populate active_signals")
                    elif hasattr(self.engines['lee_method'], 'start_scanning'):
                        await self.engines['lee_method'].start_scanning()
                        logger.info("[OK] Lee Method realtime scanner started and will populate active_signals")
            
            logger.info(f"[OK] {engine_name} engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize {engine_name} engine: {e}")
            raise

    async def process_message(self, message: str, session_id: str = None,
                            user_id: str = None, progress_id: str = None,
                            enable_progress_tracking: bool = False) -> Dict[str, Any]:
        """Process user message through AI engine with ENHANCED PROGRESS TRACKING"""
        try:
            if 'ai' not in self.engines:
                logger.error("AI engine not available in orchestrator")
                return {
                    'response': 'AI engine not available',
                    'type': 'error',
                    'confidence': 0.0,
                    'context': {}
                }

            logger.info(f"Processing message through AI engine for session {session_id}, progress_tracking: {enable_progress_tracking}")

            # Process through AI engine with orchestrator access and progress tracking
            ai_response = await self.engines['ai'].process_message(
                message=message,
                session_id=session_id,
                user_id=user_id,
                orchestrator=self,
                progress_id=progress_id,
                enable_progress_tracking=enable_progress_tracking
            )

            # Check if ai_response is None
            if ai_response is None:
                logger.error("AI engine returned None response")
                return {
                    'response': 'I apologize, but I encountered an issue generating a response. Please try again.',
                    'type': 'error',
                    'confidence': 0.0,
                    'context': {'error': 'AI engine returned None'}
                }

            # Convert AIResponse to dictionary
            result = {
                'response': getattr(ai_response, 'response', 'No response generated'),
                'type': getattr(ai_response, 'type', 'general'),
                'confidence': getattr(ai_response, 'confidence', 0.0),
                'context': getattr(ai_response, 'context', {}),
                'suggestions': getattr(ai_response, 'suggestions', None)
            }

            logger.info(f"Successfully processed message, response type: {result['type']}")
            return result

        except Exception as e:
            logger.error(f"Message processing failed: {e}", exc_info=True)
            return {
                'response': 'I encountered an error processing your message. Please try again.',
                'type': 'error',
                'confidence': 0.0,
                'context': {'error': str(e)}
            }

    async def analyze_stock(self, symbol: str, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Perform stock analysis - SYSTEM-WIDE FIX: Enhanced symbol validation"""
        try:
            # SYSTEM-WIDE FIX: Validate symbol format before processing
            from sp500_symbols import is_valid_stock_symbol

            if not is_valid_stock_symbol(symbol):
                logger.error(f"Invalid symbol format: {symbol} (must be 2-5 uppercase letters)")
                return {
                    'error': f'Invalid symbol format: "{symbol}". Stock symbols must be 2-5 uppercase letters.',
                    'symbol': symbol,
                    'valid_format': False
                }

            if 'trading' not in self.engines:
                return {'error': 'Trading engine not available'}

            # Use trading engine for analysis
            analysis = await self.engines['trading'].analyze_stock(symbol, analysis_type)
            return analysis

        except Exception as e:
            logger.error(f"Stock analysis failed for {symbol}: {e}")
            return {'error': str(e)}

    # ============================================================================
    # ADVANCED AI CAPABILITIES (v5.0)
    # ============================================================================

    async def analyze_causal_impact(self, symbol: str, intervention: Dict[str, float],
                                  time_horizon: int = 5) -> Dict[str, Any]:
        """Analyze causal impact of market interventions"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].analyze_causal_impact(symbol, intervention, time_horizon)
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Causal impact analysis failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_causal_explanation(self, symbol: str, outcome: str) -> Dict[str, Any]:
        """Get causal explanation for market outcome"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].get_causal_explanation(symbol, outcome)
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Causal explanation failed: {e}")
            return {'success': False, 'error': str(e)}

    async def analyze_market_psychology(self, symbol: str) -> Dict[str, Any]:
        """Analyze market psychology and participant behavior"""
        try:
            if 'ai' not in self.engines or 'market' not in self.engines:
                return {'success': False, 'error': 'Required engines not available'}

            # Get market data
            market_data = await self.engines['market'].get_market_data(symbol)

            # Analyze psychology
            result = await self.engines['ai'].analyze_market_psychology(symbol, market_data)
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Market psychology analysis failed: {e}")
            return {'success': False, 'error': str(e)}

    async def predict_participant_behavior(self, participant_type: str, symbol: str) -> Dict[str, Any]:
        """Predict behavior of specific market participant type"""
        try:
            if 'ai' not in self.engines or 'market' not in self.engines:
                return {'success': False, 'error': 'Required engines not available'}

            # Get market conditions
            market_conditions = {
                'market_data': await self.engines['market'].get_market_data(symbol),
                'sentiment_profile': await self.analyze_market_psychology(symbol)
            }

            # Predict behavior
            result = await self.engines['ai'].predict_participant_behavior(
                participant_type, symbol, market_conditions
            )
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Participant behavior prediction failed: {e}")
            return {'success': False, 'error': str(e)}

    async def run_autonomous_agents(self) -> Dict[str, Any]:
        """Run autonomous trading agent decision cycles"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].run_autonomous_agents()
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Autonomous agents execution failed: {e}")
            return {'success': False, 'error': str(e)}

    async def execute_agent_decisions(self, decisions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Execute decisions from autonomous agents"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].execute_agent_decisions(decisions)
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Agent decision execution failed: {e}")
            return {'success': False, 'error': str(e)}

    # ============================================================================
    # MULTIMODAL PROCESSING CAPABILITIES (v5.0 Phase 2)
    # ============================================================================

    async def process_video_content(self, video_path: str, content_type: str = 'earnings_call') -> Dict[str, Any]:
        """Process video content for financial insights"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].process_video_content(video_path, content_type)
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Video content processing failed: {e}")
            return {'success': False, 'error': str(e)}

    async def analyze_chart_image(self, image_data: bytes) -> Dict[str, Any]:
        """Analyze chart image for patterns and signals"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].analyze_chart_image(image_data)
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Chart image analysis failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_alternative_market_data(self, source_type: str, symbol: str = None) -> Dict[str, Any]:
        """Get alternative market data from various sources"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].get_alternative_market_data(source_type, symbol)
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Alternative market data retrieval failed: {e}")
            return {'success': False, 'error': str(e)}

    async def fuse_multimodal_analysis(self, symbol: str, video_path: str = None,
                                     image_data: bytes = None,
                                     alt_data_sources: List[str] = None) -> Dict[str, Any]:
        """Fuse multiple data modalities for comprehensive analysis"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].fuse_multimodal_analysis(
                symbol, video_path, image_data, alt_data_sources
            )
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Multimodal analysis fusion failed: {e}")
            return {'success': False, 'error': str(e)}

    # ============================================================================
    # EXPLAINABLE AI CAPABILITIES (v5.0 Phase 3)
    # ============================================================================

    async def explain_trading_decision(self, decision_id: str, decision_type: str,
                                     model_output: Dict[str, Any], input_features: Dict[str, Any],
                                     compliance_level: str = 'sec_compliant') -> Dict[str, Any]:
        """Generate explanation for trading decision"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].explain_trading_decision(
                decision_id, decision_type, model_output, input_features, compliance_level
            )
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Trading decision explanation failed: {e}")
            return {'success': False, 'error': str(e)}

    async def create_decision_audit_trail(self, decision_id: str, user_id: str,
                                        symbol: str, decision_type: str,
                                        input_data: Dict[str, Any], model_version: str = 'v5.0') -> Dict[str, Any]:
        """Create audit trail for trading decision"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].create_decision_audit_trail(
                decision_id, user_id, symbol, decision_type, input_data, model_version
            )
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Decision audit trail creation failed: {e}")
            return {'success': False, 'error': str(e)}

    async def generate_counterfactual_analysis(self, decision_id: str,
                                             original_features: Dict[str, Any],
                                             target_outcome: str) -> Dict[str, Any]:
        """Generate counterfactual what-if analysis"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].generate_counterfactual_analysis(
                decision_id, original_features, target_outcome
            )
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Counterfactual analysis failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_decision_audit_trails(self, symbol: str = None, decision_type: str = None,
                                      days_back: int = 30) -> Dict[str, Any]:
        """Get audit trails for decisions"""
        try:
            if 'ai' not in self.engines:
                return {'success': False, 'error': 'AI engine not available'}

            result = await self.engines['ai'].get_decision_audit_trails(
                symbol, decision_type, days_back
            )
            return {'success': True, 'data': result}

        except Exception as e:
            logger.error(f"Audit trails retrieval failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            engine_statuses = {}
            
            for engine_name, engine in self.engines.items():
                try:
                    if hasattr(engine, 'status'):
                        engine_statuses[engine_name] = engine.status.value
                    else:
                        engine_statuses[engine_name] = 'unknown'
                except:
                    engine_statuses[engine_name] = 'error'
            
            # Get advanced AI status if available
            advanced_ai_status = {}
            if 'ai' in self.engines and hasattr(self.engines['ai'], 'get_advanced_ai_status'):
                try:
                    advanced_ai_status = self.engines['ai'].get_advanced_ai_status()
                except Exception as e:
                    logger.error(f"Failed to get advanced AI status: {e}")
                    advanced_ai_status = {'error': str(e)}

            # Get multimodal processing status if available
            multimodal_status = {}
            if 'ai' in self.engines and hasattr(self.engines['ai'], 'get_multimodal_status'):
                try:
                    multimodal_status = self.engines['ai'].get_multimodal_status()
                except Exception as e:
                    logger.error(f"Failed to get multimodal status: {e}")
                    multimodal_status = {'error': str(e)}

            # Get explainable AI status if available
            explainable_ai_status = {}
            if 'ai' in self.engines and hasattr(self.engines['ai'], 'get_explainable_ai_status'):
                try:
                    explainable_ai_status = self.engines['ai'].get_explainable_ai_status()
                except Exception as e:
                    logger.error(f"Failed to get explainable AI status: {e}")
                    explainable_ai_status = {'error': str(e)}

            return {
                'timestamp': datetime.now().isoformat(),
                'orchestrator_status': self.status.value,
                'engines': engine_statuses,
                'advanced_ai': advanced_ai_status,
                'multimodal_processing': multimodal_status,
                'explainable_ai': explainable_ai_status,
                'total_engines': len(self.engines),
                'active_engines': sum(1 for status in engine_statuses.values() if status == 'active'),
                'system_health': 'healthy' if self.status == EngineStatus.ACTIVE else 'unhealthy'
            }
            
        except Exception as e:
            logger.error(f"System status check failed: {e}")
            return {'error': str(e)}

    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary with web search enhancement"""
        try:
            if 'trading' not in self.engines:
                return {'error': 'Trading engine not available'}

            # Get portfolio from trading engine
            portfolio = await self.engines['trading'].get_portfolio_summary()

            # Enhance with web search if portfolio has positions
            if portfolio.get('positions') and web_search_service.is_available():
                symbols = [pos.get('symbol') for pos in portfolio['positions'] if pos.get('symbol')]
                if symbols:
                    # Search for portfolio-relevant news
                    portfolio_intelligence = await self._enhance_portfolio_with_web_search(symbols[:5])
                    portfolio['web_intelligence'] = portfolio_intelligence

            return portfolio

        except Exception as e:
            logger.error(f"Portfolio summary failed: {e}")
            return {'error': str(e)}

    async def _enhance_portfolio_with_web_search(self, symbols: List[str]) -> Dict[str, Any]:
        """Enhance portfolio analysis with web search for company-specific news and sector trends"""
        try:
            portfolio_intelligence = {}

            for symbol in symbols:
                # Search for company-specific news
                company_news = await web_search_service.search_for_context(
                    f"{symbol} earnings revenue guidance company news",
                    SearchContext.PORTFOLIO_ANALYSIS,
                    [symbol],
                    max_results=2
                )

                # Search for sector trends
                sector_trends = await web_search_service.search_for_context(
                    f"{symbol} sector trends industry analysis market outlook",
                    SearchContext.PORTFOLIO_ANALYSIS,
                    [symbol],
                    max_results=2
                )

                portfolio_intelligence[symbol] = {
                    "company_news": [result.__dict__ for result in company_news],
                    "sector_trends": [result.__dict__ for result in sector_trends],
                    "total_sources": len(company_news) + len(sector_trends)
                }

            return {
                "web_enhanced": True,
                "symbols_analyzed": len(portfolio_intelligence),
                "intelligence": portfolio_intelligence,
                "summary": f"Enhanced portfolio analysis with web intelligence for {len(symbols)} positions"
            }

        except Exception as e:
            logger.error(f"[PORTFOLIO_WEB_SEARCH] Enhancement failed: {e}")
            return {"web_enhanced": False, "error": str(e)}

    async def get_educational_content(self, topic: str = None) -> Dict[str, Any]:
        """Get educational content"""
        try:
            if 'education' not in self.engines:
                return {'error': 'Education engine not available'}
            
            # Get content from education engine
            content = await self.engines['education'].get_educational_content(topic)
            return content
            
        except Exception as e:
            logger.error(f"Educational content retrieval failed: {e}")
            return {'error': str(e)}

    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get market data for symbol"""
        try:
            if 'market' not in self.engines:
                return {'error': 'Market engine not available'}
            
            # Get data from market engine
            data = await self.engines['market'].get_market_data(symbol)
            return data
            
        except Exception as e:
            logger.error(f"Market data retrieval failed for {symbol}: {e}")
            return {'error': str(e)}

    async def assess_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess portfolio risk"""
        try:
            if 'risk' not in self.engines:
                return {'error': 'Risk engine not available'}
            
            # Assess risk using risk engine
            risk_assessment = await self.engines['risk'].assess_portfolio_risk(portfolio_data)
            return risk_assessment
            
        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'error': str(e)}

    async def scan_lee_method(self, symbols: List[str] = None) -> Dict[str, Any]:
        """Scan for Lee Method patterns"""
        try:
            if 'lee_method' not in self.engines:
                return {'error': 'Lee Method scanner not available'}

            # Scan using Lee Method engine
            results = await self.engines['lee_method'].scan_symbols(symbols)
            return results

        except Exception as e:
            logger.error(f"Lee Method scan failed: {e}")
            return {'error': str(e)}

    async def get_lee_method_signals(self) -> Dict[str, Any]:
        """Get current Lee Method signals from the realtime scanner"""
        try:
            # ✅ FIXED: Try to get signals from the global realtime scanner first
            try:
                import atlas_server
                if hasattr(atlas_server, 'realtime_scanner') and atlas_server.realtime_scanner:
                    realtime_scanner = atlas_server.realtime_scanner

                    # Check if the realtime scanner has Lee Method scanner with active signals
                    if hasattr(realtime_scanner, 'lee_scanner'):
                        lee_scanner = realtime_scanner.lee_scanner
                        if hasattr(lee_scanner, 'active_signals') and lee_scanner.active_signals:
                            # Convert active signals to list format
                            signals = []
                            for symbol, signal in lee_scanner.active_signals.items():
                                signals.append(signal.to_dict())
                            logger.info(f"[SIGNALS] Retrieved {len(signals)} signals from global realtime scanner Lee Method scanner active_signals")
                            return {'signals': signals}
                        elif hasattr(lee_scanner, 'get_latest_signals'):
                            # Try the get_latest_signals method
                            signals = lee_scanner.get_latest_signals()
                            logger.info(f"[SIGNALS] Retrieved {len(signals)} signals from global realtime scanner Lee Method scanner get_latest_signals")
                            return {'signals': signals}

                    # Check if the realtime scanner itself has active_results with Lee Method signals
                    if hasattr(realtime_scanner, 'get_active_results'):
                        signals = await realtime_scanner.get_active_results()
                        # Filter for Lee Method signals only
                        lee_signals = [s for s in signals if s.get('pattern_type') == 'lee_method_ttm_squeeze']
                        logger.info(f"[SIGNALS] Retrieved {len(lee_signals)} Lee Method signals from global realtime scanner active_results")
                        return {'signals': lee_signals}

            except Exception as e:
                logger.warning(f"[SIGNALS] Could not access global realtime scanner: {e}")

            # Fallback to orchestrator's lee_method engine
            if 'lee_method' not in self.engines:
                return {'signals': []}

            lee_engine = self.engines['lee_method']

            # Check if this is the global realtime scanner with get_active_results method
            if hasattr(lee_engine, 'get_active_results'):
                # Get signals from active_results via the method (this is the main realtime scanner)
                signals = await lee_engine.get_active_results()
                logger.info(f"[SIGNALS] Retrieved {len(signals)} signals from orchestrator realtime scanner active_results")
                return {'signals': signals}

            elif hasattr(lee_engine, 'get_latest_signals'):
                # Fallback to the Lee Method scanner method
                signals = lee_engine.get_latest_signals()  # Get signals from active_signals
                logger.info(f"[SIGNALS] Retrieved {len(signals)} signals from orchestrator Lee Method scanner active_signals")
                return {'signals': signals}

            elif hasattr(lee_engine, 'get_active_signals'):
                # Fallback to regular method
                signals = await lee_engine.get_active_signals()
                return {'signals': signals or []}

            else:
                logger.warning("[SIGNALS] Lee Method engine has no signal retrieval method")
                return {'signals': []}

        except Exception as e:
            logger.error(f"Lee Method signals retrieval failed: {e}")
            return {'signals': []}

    async def get_trading_positions(self) -> Dict[str, Any]:
        """Get current trading positions"""
        try:
            if 'trading' not in self.engines:
                return {'positions': []}

            # Get positions from trading engine
            positions = await self.engines['trading'].get_positions()
            return {'positions': positions or []}

        except Exception as e:
            logger.error(f"Trading positions retrieval failed: {e}")
            return {'positions': []}

    async def place_trading_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Place a trading order"""
        try:
            if 'trading' not in self.engines:
                return {'success': False, 'error': 'Trading engine not available'}

            # Place order through trading engine
            result = await self.engines['trading'].place_order(
                symbol=order_data.get('symbol'),
                side=order_data.get('side'),
                quantity=order_data.get('quantity'),
                order_type=order_data.get('order_type', 'MARKET'),
                price=order_data.get('price')
            )
            return {'success': True, 'order_id': result.id if hasattr(result, 'id') else 'unknown'}

        except Exception as e:
            logger.error(f"Trading order placement failed: {e}")
            return {'success': False, 'error': str(e)}

    async def close_trading_position(self, symbol: str) -> Dict[str, Any]:
        """Close a trading position"""
        try:
            if 'trading' not in self.engines:
                return {'success': False, 'error': 'Trading engine not available'}

            # Close position through trading engine
            result = await self.engines['trading'].close_position(symbol)
            return {'success': True, 'message': f'Position closed for {symbol}'}

        except Exception as e:
            logger.error(f"Position closure failed for {symbol}: {e}")
            return {'success': False, 'error': str(e)}

    async def optimize_portfolio(self) -> Dict[str, Any]:
        """Optimize portfolio"""
        try:
            if 'trading' not in self.engines:
                return {'success': False, 'error': 'Trading engine not available'}

            # Optimize portfolio through trading engine
            result = await self.engines['trading'].optimize_portfolio()
            return {'success': True, 'message': 'Portfolio optimization completed'}

        except Exception as e:
            logger.error(f"Portfolio optimization failed: {e}")
            return {'success': False, 'error': str(e)}

    async def run_risk_assessment(self) -> Dict[str, Any]:
        """Run comprehensive risk assessment"""
        try:
            if 'risk' not in self.engines:
                return {'success': False, 'error': 'Risk engine not available'}

            # Run risk assessment through risk engine
            result = await self.engines['risk'].run_comprehensive_assessment()
            return {
                'success': True,
                'var': result.get('var', 'N/A'),
                'sharpe_ratio': result.get('sharpe_ratio', 'N/A'),
                'max_drawdown': result.get('max_drawdown', 'N/A')
            }

        except Exception as e:
            logger.error(f"Risk assessment failed: {e}")
            return {'success': False, 'error': str(e)}

    # ============================================================================
    # GROK INTEGRATION MONITORING
    # ============================================================================

    async def get_grok_integration_status(self) -> Dict[str, Any]:
        """Get comprehensive Grok integration status across all engines"""
        try:
            if not self.grok_integration_available:
                return {
                    'grok_available': False,
                    'error': 'Grok integration not available',
                    'engines_with_grok': [],
                    'performance_metrics': self.grok_performance_metrics
                }

            grok_status = {
                'grok_available': True,
                'engines_with_grok': [],
                'engine_statuses': {},
                'performance_metrics': self.grok_performance_metrics,
                'timestamp': datetime.now().isoformat()
            }

            # Check Grok status in each engine
            for engine_name, engine in self.engines.items():
                if hasattr(engine, 'grok_integration_available') and engine.grok_integration_available:
                    grok_status['engines_with_grok'].append(engine_name)

                    # Get engine-specific Grok status
                    if hasattr(engine, 'get_grok_integration_status'):
                        grok_status['engine_statuses'][engine_name] = engine.get_grok_integration_status()
                    elif hasattr(engine, 'grok_engine') and engine.grok_engine:
                        grok_status['engine_statuses'][engine_name] = engine.grok_engine.get_engine_status()

            # Update performance metrics
            await self._update_grok_performance_metrics(grok_status)

            return grok_status

        except Exception as e:
            logger.error(f"Grok integration status check failed: {e}")
            return {'error': str(e)}

    async def _update_grok_performance_metrics(self, grok_status: Dict[str, Any]):
        """Update Grok performance metrics"""
        try:
            total_enhancements = 0
            successful_enhancements = 0
            improvements = []

            for engine_name, engine_status in grok_status.get('engine_statuses', {}).items():
                if isinstance(engine_status, dict):
                    # Extract performance metrics from engine status
                    perf_metrics = engine_status.get('performance_metrics', {})
                    if isinstance(perf_metrics, dict):
                        total_enhancements += perf_metrics.get('total_enhancements', 0)
                        avg_improvement = perf_metrics.get('average_improvement', 0.0)
                        if avg_improvement > 0:
                            improvements.append(avg_improvement)
                            successful_enhancements += 1

            # Update metrics
            self.grok_performance_metrics.update({
                'total_enhancements': total_enhancements,
                'successful_enhancements': successful_enhancements,
                'average_improvement': sum(improvements) / len(improvements) if improvements else 0.0,
                'last_update': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Grok performance metrics update failed: {e}")

    async def get_enhanced_prediction(self, symbol: str, prediction_type: str = "price_movement") -> Dict[str, Any]:
        """Get enhanced prediction using Grok across available engines"""
        try:
            if not self.engines.get('ai'):
                return {'error': 'AI engine not available'}

            # Use AI engine's enhanced prediction capability
            if hasattr(self.engines['ai'], 'get_enhanced_prediction'):
                market_data = {}

                # Gather market data from market engine if available
                if self.engines.get('market'):
                    try:
                        market_data = await self.engines['market'].get_market_data(symbol)
                    except Exception as e:
                        logger.warning(f"Market data gathering failed: {e}")

                result = await self.engines['ai'].get_enhanced_prediction(
                    symbol, market_data, prediction_type
                )

                return result
            else:
                return {'error': 'Enhanced prediction not available'}

        except Exception as e:
            logger.error(f"Enhanced prediction failed for {symbol}: {e}")
            return {'error': str(e)}

    async def optimize_ml_models(self) -> Dict[str, Any]:
        """Optimize ML models using Grok code generation"""
        try:
            if not self.engines.get('ai'):
                return {'error': 'AI engine not available'}

            # Check if AI engine has ML analytics with Grok optimization
            if hasattr(self.engines['ai'], 'ml_analytics') and hasattr(self.engines['ai'].ml_analytics, 'optimize_existing_models'):
                result = await self.engines['ai'].ml_analytics.optimize_existing_models()
                return result
            else:
                return {'error': 'ML model optimization not available'}

        except Exception as e:
            logger.error(f"ML model optimization failed: {e}")
            return {'error': str(e)}

    async def shutdown(self):
        """Shutdown all engines gracefully"""
        try:
            logger.info("[SHUTDOWN] Starting A.T.L.A.S. system shutdown...")
            
            # Shutdown engines in reverse order
            for engine_name in reversed(self.initialization_order):
                if engine_name in self.engines:
                    try:
                        engine = self.engines[engine_name]
                        if hasattr(engine, 'shutdown'):
                            await engine.shutdown()
                        logger.info(f"[SHUTDOWN] {engine_name} engine shutdown completed")
                    except Exception as e:
                        logger.error(f"Error shutting down {engine_name} engine: {e}")

            # Cleanup web search service
            try:
                if hasattr(self, 'web_search_service') and self.web_search_service:
                    await self.web_search_service.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up web search service: {e}")

            self.status = EngineStatus.INACTIVE
            logger.info("[SHUTDOWN] A.T.L.A.S. system shutdown completed")
            
        except Exception as e:
            logger.error(f"System shutdown error: {e}")

    def get_engine(self, engine_name: str):
        """Get specific engine instance"""
        return self.engines.get(engine_name)

    def is_ready(self) -> bool:
        """Check if system is ready"""
        return self.status == EngineStatus.ACTIVE

    def get_engine_count(self) -> int:
        """Get total number of engines"""
        return len(self.engines)

    def get_active_engine_count(self) -> int:
        """Get number of active engines"""
        active_count = 0
        for engine in self.engines.values():
            try:
                if hasattr(engine, 'status') and engine.status == EngineStatus.ACTIVE:
                    active_count += 1
            except Exception as e:
                logger.debug(f"Error checking engine status: {e}")
                continue
        return active_count

    async def get_comprehensive_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status including Grok integration"""
        try:
            # Basic system status
            system_status = {
                'orchestrator_status': self.status.value,
                'total_engines': self.get_engine_count(),
                'active_engines': self.get_active_engine_count(),
                'system_ready': self.is_ready(),
                'timestamp': datetime.now().isoformat()
            }

            # Engine statuses
            engine_statuses = {}
            for engine_name, engine in self.engines.items():
                try:
                    if hasattr(engine, 'status'):
                        engine_statuses[engine_name] = engine.status.value
                    else:
                        engine_statuses[engine_name] = 'unknown'
                except:
                    engine_statuses[engine_name] = 'error'

            system_status['engine_statuses'] = engine_statuses

            # Grok integration status
            if self.grok_integration_available:
                grok_status = await self.get_grok_integration_status()
                system_status['grok_integration'] = grok_status
            else:
                system_status['grok_integration'] = {
                    'grok_available': False,
                    'error': 'Grok integration not available'
                }

            return system_status

        except Exception as e:
            logger.error(f"Comprehensive system status failed: {e}")

    # ============================================================================
    # NEWS INSIGHTS METHODS
    # ============================================================================

    async def get_news_insights(self, symbols: List[str] = None, analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """Get comprehensive news insights and analysis"""
        try:
            if 'news_insights' not in self.engines:
                return {
                    'success': False,
                    'error': 'News Insights engine not available',
                    'fallback_data': {
                        'message': 'News analysis temporarily unavailable',
                        'suggestion': 'Try basic market data queries instead'
                    }
                }

            news_engine = self.engines['news_insights']
            return await news_engine.get_news_insights(symbols, analysis_type)

        except Exception as e:
            logger.error(f"News insights request failed: {e}")
            return {'success': False, 'error': str(e)}

    async def analyze_news_sentiment(self, symbols: List[str] = None) -> Dict[str, Any]:
        """Analyze news sentiment for given symbols"""
        try:
            if 'news_insights' not in self.engines:
                return {
                    'success': False,
                    'error': 'News Insights engine not available',
                    'fallback_sentiment': 'neutral'
                }

            news_engine = self.engines['news_insights']
            return await news_engine.analyze_news_sentiment(symbols)

        except Exception as e:
            logger.error(f"News sentiment analysis failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_market_news_alerts(self, severity: str = "medium") -> Dict[str, Any]:
        """Get current market news alerts"""
        try:
            if 'news_insights' not in self.engines:
                return {
                    'success': False,
                    'error': 'News Insights engine not available',
                    'alerts': []
                }

            # For now, return simulated alerts
            # In production, this would call the news engine's alert system
            return {
                'success': True,
                'alerts': [
                    {
                        'id': 'alert_001',
                        'severity': severity,
                        'title': 'Federal Reserve Policy Update',
                        'description': 'Latest Fed announcement may impact market sentiment',
                        'timestamp': datetime.now().isoformat(),
                        'symbols': ['SPY', 'QQQ'],
                        'impact_score': 0.8
                    }
                ],
                'alert_count': 1,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"News alerts request failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_news_insights_status(self) -> Dict[str, Any]:
        """Get News Insights engine status and capabilities"""
        try:
            status = {
                'engine_available': 'news_insights' in self.engines,
                'news_insights_integration': self.news_insights_available,
                'capabilities': {
                    'real_time_ingestion': True,
                    'sentiment_analysis': True,
                    'market_impact_scoring': True,
                    'theme_clustering': True,
                    'alert_generation': True,
                    'multi_source_correlation': True
                },
                'data_sources': {
                    'financial_modeling_prep': True,
                    'alpha_vantage': True,
                    'sec_edgar': True,
                    'social_media': True,
                    'economic_calendar': True
                },
                'last_updated': datetime.now().isoformat()
            }

            if 'news_insights' in self.engines:
                # Get additional status from the engine
                news_engine = self.engines['news_insights']
                if hasattr(news_engine, 'news_engine'):
                    engine_status = news_engine.news_engine.status
                    status['engine_status'] = engine_status.value if hasattr(engine_status, 'value') else str(engine_status)
                    status['configuration'] = news_engine.news_engine.config

            return status

        except Exception as e:
            logger.error(f"News insights status check failed: {e}")
            return {
                'engine_available': False,
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            }

    # ============================================================================
    # COMPREHENSIVE TRADING PLAN METHODS
    # ============================================================================

    async def generate_trading_plan(self, target_profit: float, timeframe_days: int,
                                  starting_capital: float, risk_tolerance: str = "moderate") -> Dict[str, Any]:
        """Generate comprehensive trading plan through trading plan engine"""
        try:
            if 'trading_plan' not in self.engines:
                return {'success': False, 'error': 'Trading plan engine not available'}

            # Generate trading plan
            trading_plan = await self.engines['trading_plan'].generate_comprehensive_trading_plan(
                target_profit=target_profit,
                timeframe_days=timeframe_days,
                starting_capital=starting_capital,
                risk_tolerance=risk_tolerance
            )

            return {
                'success': True,
                'trading_plan': trading_plan.dict(),
                'plan_id': trading_plan.plan_id,
                'opportunities_count': len(trading_plan.opportunities),
                'total_expected_return': trading_plan.total_expected_return,
                'plan_confidence': trading_plan.plan_confidence
            }

        except Exception as e:
            logger.error(f"Trading plan generation failed: {e}")
            return {'success': False, 'error': str(e)}

    async def get_trading_plan(self, plan_id: str) -> Dict[str, Any]:
        """Retrieve specific trading plan"""
        try:
            if 'trading_plan' not in self.engines:
                return {'success': False, 'error': 'Trading plan engine not available'}

            trading_plan = await self.engines['trading_plan'].get_active_plan(plan_id)

            if not trading_plan:
                return {'success': False, 'error': f'Trading plan {plan_id} not found'}

            return {
                'success': True,
                'trading_plan': trading_plan.dict()
            }

        except Exception as e:
            logger.error(f"Trading plan retrieval failed: {e}")
            return {'success': False, 'error': str(e)}

    async def detect_trading_plan_intent(self, message: str) -> Dict[str, Any]:
        """Detect if user message is requesting a trading plan"""
        try:
            # Keywords that indicate trading plan request
            plan_keywords = [
                "trading plan", "make money", "profit", "generate", "earn",
                "trading strategy", "actionable plan", "dollar target", "timeframe",
                "specific amount", "trading goal", "investment plan"
            ]

            # Amount patterns (dollar amounts)
            import re
            amount_patterns = [
                r'\$[\d,]+',  # $1,000
                r'[\d,]+\s*dollars?',  # 1000 dollars
                r'[\d,]+\s*bucks?',  # 1000 bucks
            ]

            # Timeframe patterns
            timeframe_patterns = [
                r'(\d+)\s*days?',
                r'(\d+)\s*weeks?',
                r'(\d+)\s*months?',
                r'by\s+(\w+)',  # by Friday
                r'within\s+(\d+)',
                r'in\s+(\d+)'
            ]

            message_lower = message.lower()

            # Check for plan keywords
            has_plan_keywords = any(keyword in message_lower for keyword in plan_keywords)

            # Extract dollar amounts
            amounts = []
            for pattern in amount_patterns:
                matches = re.findall(pattern, message, re.IGNORECASE)
                for match in matches:
                    # Extract numeric value
                    numeric = re.sub(r'[^\d]', '', match)
                    if numeric:
                        amounts.append(int(numeric))

            # Extract timeframes
            timeframes = []
            for pattern in timeframe_patterns:
                matches = re.findall(pattern, message, re.IGNORECASE)
                timeframes.extend(matches)

            # Determine if this is a trading plan request
            is_trading_plan_request = (
                has_plan_keywords and
                (amounts or timeframes or
                 any(word in message_lower for word in ["specific", "target", "goal"]))
            )

            return {
                'is_trading_plan_request': is_trading_plan_request,
                'detected_amounts': amounts,
                'detected_timeframes': timeframes,
                'confidence': 0.9 if has_plan_keywords and amounts else 0.6 if has_plan_keywords else 0.1
            }

        except Exception as e:
            logger.error(f"Trading plan intent detection failed: {e}")
            return {
                'is_trading_plan_request': False,
                'detected_amounts': [],
                'detected_timeframes': [],
                'confidence': 0.0
            }


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = ["AtlasOrchestrator"]
