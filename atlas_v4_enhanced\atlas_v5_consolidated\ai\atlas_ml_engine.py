"""
A.T.L.A.S ML Analytics - Consolidated Machine Learning and Analytics
Combines ML Predictor, Sentiment Analyzer, and Options Flow Analyzer
"""

import asyncio
import logging
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
import numpy as np
import pandas as pd

# Add helper tools to path
sys.path.append(os.path.join(os.path.dirname(__file__), '4_helper_tools'))

from config import settings
from models import EngineStatus

# Optional imports with graceful fallbacks
try:
    from transformers import pipeline
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, TensorDataset
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# Advanced ML imports
try:
    from sklearn.preprocessing import MinMaxScaler, StandardScaler
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    from sklearn.model_selection import TimeSeriesSplit
    import xgboost as xgb
    ADVANCED_ML_AVAILABLE = True
except ImportError:
    ADVANCED_ML_AVAILABLE = False

# Grok integration (with graceful fallback)
try:
    from atlas_grok_integration import AtlasGrokIntegrationEngine, GrokTaskType, GrokCapability
    GROK_INTEGRATION_AVAILABLE = True
except ImportError:
    GROK_INTEGRATION_AVAILABLE = False

# Time series analysis imports
try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.seasonal import seasonal_decompose
    import scipy.stats as stats
    TIME_SERIES_AVAILABLE = True
except ImportError:
    TIME_SERIES_AVAILABLE = False

logger = logging.getLogger(__name__)


# ============================================================================
# ML PREDICTOR ENGINE
# ============================================================================

class AtlasMLPredictor:
    """Machine learning price prediction engine"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.models = {}
        self.predictions_cache = {}
        self.ml_enabled = settings.ML_MODELS_ENABLED if hasattr(settings, 'ML_MODELS_ENABLED') else False
        
        logger.info(f"[AI] LSTM Predictor initialized - ML: {TORCH_AVAILABLE}")

    async def initialize(self):
        """Initialize ML models"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            if self.ml_enabled and TORCH_AVAILABLE:
                await self._load_ml_models()
                logger.info("[OK] ML models loaded successfully")
            else:
                logger.info("[AI] LSTM predictor running in fallback mode (no ML models)")
            
            self.status = EngineStatus.ACTIVE
            
        except Exception as e:
            logger.error(f"ML Predictor initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _load_ml_models(self):
        """Load pre-trained ML models"""
        try:
            # Placeholder for actual model loading
            # In production, this would load trained LSTM/GRU models
            self.models['lstm_price'] = None  # Would load actual model
            self.models['volatility'] = None  # Would load volatility model
            logger.info("[ML] Models loaded (placeholder)")
            
        except Exception as e:
            logger.error(f"Model loading failed: {e}")
            raise

    async def predict_price(self, symbol: str, timeframe: str = '1d', 
                          horizon: int = 5) -> Dict[str, Any]:
        """Predict future price movements"""
        try:
            # Check cache first
            cache_key = f"{symbol}_{timeframe}_{horizon}"
            if cache_key in self.predictions_cache:
                cached = self.predictions_cache[cache_key]
                if (datetime.now() - cached['timestamp']).seconds < 300:  # 5 min cache
                    return cached['prediction']
            
            if self.ml_enabled and self.models.get('lstm_price'):
                # Use actual ML model
                prediction = await self._ml_predict_price(symbol, timeframe, horizon)
            else:
                # Fallback to statistical prediction
                prediction = await self._statistical_predict_price(symbol, timeframe, horizon)
            
            # Cache prediction
            self.predictions_cache[cache_key] = {
                'prediction': prediction,
                'timestamp': datetime.now()
            }
            
            return prediction
            
        except Exception as e:
            logger.error(f"Price prediction failed for {symbol}: {e}")
            return {'error': str(e)}

    async def _ml_predict_price(self, symbol: str, timeframe: str, horizon: int) -> Dict[str, Any]:
        """ML-based price prediction"""
        try:
            # Placeholder for actual ML prediction
            # Would use trained LSTM/GRU model here
            
            # Simulate ML prediction
            base_price = 150.0  # Would get from actual data
            confidence = 0.75
            
            predictions = []
            for i in range(horizon):
                # Simulate price movement
                change = np.random.normal(0.001, 0.02)  # 0.1% mean, 2% std
                predicted_price = base_price * (1 + change)
                predictions.append({
                    'day': i + 1,
                    'predicted_price': round(predicted_price, 2),
                    'confidence': confidence - (i * 0.05)  # Decreasing confidence
                })
                base_price = predicted_price
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'horizon_days': horizon,
                'predictions': predictions,
                'model_type': 'LSTM',
                'overall_confidence': confidence,
                'direction': 'bullish' if predictions[-1]['predicted_price'] > 150.0 else 'bearish'
            }
            
        except Exception as e:
            logger.error(f"ML prediction failed: {e}")
            raise

    async def _statistical_predict_price(self, symbol: str, timeframe: str, horizon: int) -> Dict[str, Any]:
        """Statistical fallback prediction"""
        try:
            # Simple statistical prediction based on historical patterns
            base_price = 150.0  # Would get from market data
            volatility = 0.02  # 2% daily volatility
            
            predictions = []
            for i in range(horizon):
                # Random walk with slight upward bias
                change = np.random.normal(0.0005, volatility)  # 0.05% daily drift
                predicted_price = base_price * (1 + change)
                predictions.append({
                    'day': i + 1,
                    'predicted_price': round(predicted_price, 2),
                    'confidence': 0.6 - (i * 0.05)  # Lower confidence for statistical
                })
                base_price = predicted_price
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'horizon_days': horizon,
                'predictions': predictions,
                'model_type': 'Statistical',
                'overall_confidence': 0.6,
                'direction': 'neutral',
                'note': 'Using statistical fallback - ML models not available'
            }
            
        except Exception as e:
            logger.error(f"Statistical prediction failed: {e}")
            return {'error': str(e)}


# ============================================================================
# ADVANCED NEURAL NETWORK ARCHITECTURES (v5.0 Enhancement)
# ============================================================================

class AtlasLSTMModel(nn.Module):
    """Advanced LSTM model for time series prediction"""

    def __init__(self, input_size: int, hidden_size: int = 128, num_layers: int = 3,
                 output_size: int = 1, dropout: float = 0.2):
        super(AtlasLSTMModel, self).__init__()

        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM layers with dropout
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)

        # Attention mechanism
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, dropout=dropout)

        # Dense layers
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, hidden_size // 4)
        self.fc3 = nn.Linear(hidden_size // 4, output_size)

        # Regularization
        self.dropout = nn.Dropout(dropout)
        self.batch_norm = nn.BatchNorm1d(hidden_size // 2)

    def forward(self, x):
        # LSTM forward pass
        lstm_out, (hidden, cell) = self.lstm(x)

        # Apply attention
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)

        # Use last output
        out = attn_out[:, -1, :]

        # Dense layers with regularization
        out = torch.relu(self.fc1(out))
        out = self.batch_norm(out)
        out = self.dropout(out)

        out = torch.relu(self.fc2(out))
        out = self.dropout(out)

        out = self.fc3(out)

        return out

class AtlasTransformerModel(nn.Module):
    """Transformer model for financial time series"""

    def __init__(self, input_size: int, d_model: int = 256, nhead: int = 8,
                 num_layers: int = 6, output_size: int = 1, dropout: float = 0.1):
        super(AtlasTransformerModel, self).__init__()

        self.d_model = d_model
        self.input_projection = nn.Linear(input_size, d_model)

        # Positional encoding
        self.pos_encoding = self._generate_positional_encoding(1000, d_model)

        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, nhead=nhead, dropout=dropout,
            dim_feedforward=d_model * 4, activation='gelu'
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

        # Output layers
        self.output_projection = nn.Linear(d_model, output_size)
        self.dropout = nn.Dropout(dropout)

    def _generate_positional_encoding(self, max_len: int, d_model: int):
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-np.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        return pe.unsqueeze(0)

    def forward(self, x):
        seq_len = x.size(1)

        # Project input to model dimension
        x = self.input_projection(x)

        # Add positional encoding
        x = x + self.pos_encoding[:, :seq_len, :].to(x.device)

        # Transformer forward pass
        x = x.transpose(0, 1)  # (seq_len, batch, d_model)
        x = self.transformer(x)
        x = x.transpose(0, 1)  # (batch, seq_len, d_model)

        # Use last output
        x = x[:, -1, :]
        x = self.dropout(x)

        # Output projection
        output = self.output_projection(x)

        return output

class AtlasAdvancedMLEngine:
    """Advanced ML engine with LSTM, Transformers, and real-time learning"""

    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.torch_available = TORCH_AVAILABLE
        self.advanced_ml_available = ADVANCED_ML_AVAILABLE

        # Models
        self.lstm_model = None
        self.transformer_model = None
        self.ensemble_models = {}

        # Training components
        self.scaler = StandardScaler() if ADVANCED_ML_AVAILABLE else None
        self.optimizer = None
        self.criterion = nn.MSELoss() if TORCH_AVAILABLE else None

        # Real-time learning
        self.online_buffer = []
        self.buffer_size = 1000
        self.retrain_threshold = 100
        self.performance_history = []

        # Model configurations
        self.lstm_config = {
            'input_size': 20,  # Number of features
            'hidden_size': 128,
            'num_layers': 3,
            'output_size': 1,
            'dropout': 0.2
        }

        self.transformer_config = {
            'input_size': 20,
            'd_model': 256,
            'nhead': 8,
            'num_layers': 6,
            'output_size': 1,
            'dropout': 0.1
        }

        logger.info(f"[ADVANCED_ML] Advanced ML Engine initialized - torch: {self.torch_available}, ml: {self.advanced_ml_available}")

    async def initialize(self):
        """Initialize advanced ML components"""
        try:
            self.status = EngineStatus.INITIALIZING

            if self.torch_available:
                await self._initialize_neural_networks()
                logger.info("[OK] Neural networks initialized")

            if self.advanced_ml_available:
                await self._initialize_ensemble_models()
                logger.info("[OK] Ensemble models initialized")

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Advanced ML Engine fully initialized")

        except Exception as e:
            logger.error(f"Advanced ML engine initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _initialize_neural_networks(self):
        """Initialize LSTM and Transformer models"""
        try:
            # Initialize LSTM model
            self.lstm_model = AtlasLSTMModel(**self.lstm_config)

            # Initialize Transformer model
            self.transformer_model = AtlasTransformerModel(**self.transformer_config)

            # Initialize optimizers
            if self.lstm_model:
                self.lstm_optimizer = optim.AdamW(self.lstm_model.parameters(),
                                                lr=0.001, weight_decay=0.01)

            if self.transformer_model:
                self.transformer_optimizer = optim.AdamW(self.transformer_model.parameters(),
                                                       lr=0.0001, weight_decay=0.01)

            logger.info("[NEURAL] Neural networks initialized")

        except Exception as e:
            logger.error(f"Neural network initialization failed: {e}")
            self.torch_available = False

    async def _initialize_ensemble_models(self):
        """Initialize ensemble models"""
        try:
            # Random Forest
            self.ensemble_models['random_forest'] = RandomForestRegressor(
                n_estimators=100, max_depth=10, random_state=42
            )

            # Gradient Boosting
            self.ensemble_models['gradient_boosting'] = GradientBoostingRegressor(
                n_estimators=100, learning_rate=0.1, max_depth=6, random_state=42
            )

            # XGBoost
            if 'xgb' in globals():
                self.ensemble_models['xgboost'] = xgb.XGBRegressor(
                    n_estimators=100, learning_rate=0.1, max_depth=6, random_state=42
                )

            logger.info("[ENSEMBLE] Ensemble models initialized")

        except Exception as e:
            logger.error(f"Ensemble model initialization failed: {e}")
            self.advanced_ml_available = False

    async def predict_with_lstm(self, features: np.ndarray, sequence_length: int = 60) -> Dict[str, Any]:
        """Make predictions using LSTM model"""
        try:
            if not self.torch_available or not self.lstm_model:
                return {'error': 'LSTM model not available'}

            # Prepare data
            if len(features.shape) == 2:
                features = features.reshape(1, features.shape[0], features.shape[1])

            # Convert to tensor
            X_tensor = torch.FloatTensor(features)

            # Make prediction
            self.lstm_model.eval()
            with torch.no_grad():
                prediction = self.lstm_model(X_tensor)
                confidence = torch.sigmoid(prediction).item()

            return {
                'prediction': prediction.item(),
                'confidence': confidence,
                'model_type': 'lstm',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"LSTM prediction failed: {e}")
            return {'error': str(e)}

    async def predict_with_transformer(self, features: np.ndarray) -> Dict[str, Any]:
        """Make predictions using Transformer model"""
        try:
            if not self.torch_available or not self.transformer_model:
                return {'error': 'Transformer model not available'}

            # Prepare data
            if len(features.shape) == 2:
                features = features.reshape(1, features.shape[0], features.shape[1])

            # Convert to tensor
            X_tensor = torch.FloatTensor(features)

            # Make prediction
            self.transformer_model.eval()
            with torch.no_grad():
                prediction = self.transformer_model(X_tensor)
                confidence = torch.sigmoid(prediction).item()

            return {
                'prediction': prediction.item(),
                'confidence': confidence,
                'model_type': 'transformer',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Transformer prediction failed: {e}")
            return {'error': str(e)}

    async def predict_with_ensemble(self, features: np.ndarray) -> Dict[str, Any]:
        """Make predictions using ensemble models"""
        try:
            if not self.advanced_ml_available:
                return {'error': 'Ensemble models not available'}

            predictions = {}
            confidences = {}

            # Flatten features for sklearn models
            if len(features.shape) > 2:
                features_flat = features.reshape(features.shape[0], -1)
            else:
                features_flat = features

            # Make predictions with each model
            for model_name, model in self.ensemble_models.items():
                try:
                    if hasattr(model, 'predict'):
                        pred = model.predict(features_flat)
                        predictions[model_name] = pred[0] if len(pred) > 0 else 0.0

                        # Calculate confidence (simplified)
                        if hasattr(model, 'predict_proba'):
                            proba = model.predict_proba(features_flat)
                            confidences[model_name] = np.max(proba[0]) if len(proba) > 0 else 0.5
                        else:
                            confidences[model_name] = 0.75  # Default confidence

                except Exception as e:
                    logger.warning(f"Prediction failed for {model_name}: {e}")
                    continue

            if not predictions:
                return {'error': 'No ensemble predictions available'}

            # Calculate weighted average
            weights = list(confidences.values())
            weighted_pred = np.average(list(predictions.values()), weights=weights)
            avg_confidence = np.mean(list(confidences.values()))

            return {
                'prediction': weighted_pred,
                'confidence': avg_confidence,
                'individual_predictions': predictions,
                'individual_confidences': confidences,
                'model_type': 'ensemble',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Ensemble prediction failed: {e}")
            return {'error': str(e)}

    async def hybrid_prediction(self, features: np.ndarray) -> Dict[str, Any]:
        """Make hybrid prediction using all available models"""
        try:
            predictions = []
            confidences = []
            model_results = {}

            # LSTM prediction
            lstm_result = await self.predict_with_lstm(features)
            if 'prediction' in lstm_result:
                predictions.append(lstm_result['prediction'])
                confidences.append(lstm_result['confidence'])
                model_results['lstm'] = lstm_result

            # Transformer prediction
            transformer_result = await self.predict_with_transformer(features)
            if 'prediction' in transformer_result:
                predictions.append(transformer_result['prediction'])
                confidences.append(transformer_result['confidence'])
                model_results['transformer'] = transformer_result

            # Ensemble prediction
            ensemble_result = await self.predict_with_ensemble(features)
            if 'prediction' in ensemble_result:
                predictions.append(ensemble_result['prediction'])
                confidences.append(ensemble_result['confidence'])
                model_results['ensemble'] = ensemble_result

            if not predictions:
                return {'error': 'No predictions available from any model'}

            # Calculate final hybrid prediction
            weights = np.array(confidences)
            weights = weights / np.sum(weights)  # Normalize weights

            final_prediction = np.average(predictions, weights=weights)
            final_confidence = np.mean(confidences)

            return {
                'prediction': final_prediction,
                'confidence': final_confidence,
                'model_results': model_results,
                'model_weights': weights.tolist(),
                'model_type': 'hybrid',
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Hybrid prediction failed: {e}")
            return {'error': str(e)}

    async def online_learning_update(self, features: np.ndarray, target: float):
        """Update models with new data for online learning"""
        try:
            # Add to online buffer
            self.online_buffer.append({
                'features': features,
                'target': target,
                'timestamp': datetime.now()
            })

            # Limit buffer size
            if len(self.online_buffer) > self.buffer_size:
                self.online_buffer = self.online_buffer[-self.buffer_size:]

            # Check if retrain threshold is reached
            if len(self.online_buffer) >= self.retrain_threshold:
                await self._retrain_models()

            return {'success': True, 'buffer_size': len(self.online_buffer)}

        except Exception as e:
            logger.error(f"Online learning update failed: {e}")
            return {'error': str(e)}

    async def _retrain_models(self):
        """Retrain models with accumulated online data"""
        try:
            if len(self.online_buffer) < 10:  # Need minimum data
                return

            # Prepare training data
            features_list = [item['features'] for item in self.online_buffer]
            targets_list = [item['target'] for item in self.online_buffer]

            X = np.array(features_list)
            y = np.array(targets_list)

            # Retrain ensemble models
            if self.advanced_ml_available:
                await self._retrain_ensemble_models(X, y)

            # Retrain neural networks (simplified)
            if self.torch_available:
                await self._retrain_neural_networks(X, y)

            # Clear buffer after retraining
            self.online_buffer = []

            logger.info("[RETRAIN] Models retrained with online data")

        except Exception as e:
            logger.error(f"Model retraining failed: {e}")

    async def _retrain_ensemble_models(self, X: np.ndarray, y: np.ndarray):
        """Retrain ensemble models"""
        try:
            # Flatten features for sklearn models
            if len(X.shape) > 2:
                X_flat = X.reshape(X.shape[0], -1)
            else:
                X_flat = X

            for model_name, model in self.ensemble_models.items():
                try:
                    model.fit(X_flat, y)
                    logger.info(f"[RETRAIN] {model_name} retrained")
                except Exception as e:
                    logger.warning(f"Retraining failed for {model_name}: {e}")

        except Exception as e:
            logger.error(f"Ensemble model retraining failed: {e}")

    async def _retrain_neural_networks(self, X: np.ndarray, y: np.ndarray):
        """Retrain neural networks (simplified online learning)"""
        try:
            # Convert to tensors
            X_tensor = torch.FloatTensor(X)
            y_tensor = torch.FloatTensor(y).unsqueeze(1)

            # Create data loader
            dataset = TensorDataset(X_tensor, y_tensor)
            dataloader = DataLoader(dataset, batch_size=32, shuffle=True)

            # Train LSTM model
            if self.lstm_model and hasattr(self, 'lstm_optimizer'):
                self.lstm_model.train()
                for batch_X, batch_y in dataloader:
                    self.lstm_optimizer.zero_grad()
                    outputs = self.lstm_model(batch_X)
                    loss = self.criterion(outputs, batch_y)
                    loss.backward()
                    self.lstm_optimizer.step()

            # Train Transformer model
            if self.transformer_model and hasattr(self, 'transformer_optimizer'):
                self.transformer_model.train()
                for batch_X, batch_y in dataloader:
                    self.transformer_optimizer.zero_grad()
                    outputs = self.transformer_model(batch_X)
                    loss = self.criterion(outputs, batch_y)
                    loss.backward()
                    self.transformer_optimizer.step()

        except Exception as e:
            logger.error(f"Neural network retraining failed: {e}")

    def get_model_performance(self) -> Dict[str, Any]:
        """Get performance metrics for all models"""
        try:
            return {
                'lstm_available': self.lstm_model is not None,
                'transformer_available': self.transformer_model is not None,
                'ensemble_models': list(self.ensemble_models.keys()),
                'online_buffer_size': len(self.online_buffer),
                'retrain_threshold': self.retrain_threshold,
                'performance_history': self.performance_history[-10:],  # Last 10 records
                'torch_available': self.torch_available,
                'advanced_ml_available': self.advanced_ml_available
            }

        except Exception as e:
            logger.error(f"Performance metrics retrieval failed: {e}")
            return {'error': str(e)}

    def get_engine_status(self) -> Dict[str, Any]:
        """Get advanced ML engine status"""
        return {
            'status': self.status.value,
            'torch_available': self.torch_available,
            'advanced_ml_available': self.advanced_ml_available,
            'models_initialized': {
                'lstm': self.lstm_model is not None,
                'transformer': self.transformer_model is not None,
                'ensemble_count': len(self.ensemble_models)
            },
            'online_learning': {
                'buffer_size': len(self.online_buffer),
                'retrain_threshold': self.retrain_threshold
            }
        }

# ============================================================================
# SENTIMENT ANALYZER
# ============================================================================

class AtlasSentimentAnalyzer:
    """Advanced sentiment analysis for market data"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.sentiment_model = None
        self.sentiment_cache = {}
        
        logger.info(f"[THEATER] Sentiment Analyzer initialized - ML: {TRANSFORMERS_AVAILABLE}")

    async def initialize(self):
        """Initialize sentiment analysis models"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            if TRANSFORMERS_AVAILABLE:
                await self._load_sentiment_model()
                logger.info("[OK] DistilBERT sentiment model loaded successfully")
            else:
                logger.info("[FALLBACK] Using basic sentiment analysis")
            
            self.status = EngineStatus.ACTIVE
            
        except Exception as e:
            logger.error(f"Sentiment Analyzer initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def _load_sentiment_model(self):
        """Load DistilBERT sentiment model"""
        try:
            logger.info("[TOOL] Loading DistilBERT sentiment model...")
            self.sentiment_model = pipeline(
                "sentiment-analysis",
                model="distilbert-base-uncased-finetuned-sst-2-english",
                device=-1  # Use CPU
            )
            logger.info("[OK] DistilBERT sentiment model loaded successfully")
            
        except Exception as e:
            logger.error(f"Sentiment model loading failed: {e}")
            self.sentiment_model = None

    async def analyze_sentiment(self, text: str, symbol: str = None) -> Dict[str, Any]:
        """Analyze sentiment of text"""
        try:
            if self.sentiment_model:
                # Use DistilBERT model
                result = self.sentiment_model(text)[0]
                
                sentiment_score = result['score']
                if result['label'] == 'NEGATIVE':
                    sentiment_score = -sentiment_score
                
                return {
                    'text': text[:100] + '...' if len(text) > 100 else text,
                    'sentiment': result['label'].lower(),
                    'confidence': result['score'],
                    'sentiment_score': sentiment_score,
                    'model': 'DistilBERT',
                    'symbol': symbol
                }
            else:
                # Fallback to basic sentiment
                return await self._basic_sentiment_analysis(text, symbol)
                
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {e}")
            return await self._basic_sentiment_analysis(text, symbol)

    async def _basic_sentiment_analysis(self, text: str, symbol: str = None) -> Dict[str, Any]:
        """Basic sentiment analysis fallback"""
        try:
            text_lower = text.lower()
            
            # Simple keyword-based sentiment
            positive_words = ['good', 'great', 'excellent', 'bullish', 'buy', 'strong', 'growth', 'profit']
            negative_words = ['bad', 'terrible', 'bearish', 'sell', 'weak', 'loss', 'decline', 'crash']
            
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count:
                sentiment = 'positive'
                score = min(0.8, 0.5 + (positive_count - negative_count) * 0.1)
            elif negative_count > positive_count:
                sentiment = 'negative'
                score = min(0.8, 0.5 + (negative_count - positive_count) * 0.1)
            else:
                sentiment = 'neutral'
                score = 0.5
            
            return {
                'text': text[:100] + '...' if len(text) > 100 else text,
                'sentiment': sentiment,
                'confidence': score,
                'sentiment_score': score if sentiment == 'positive' else -score if sentiment == 'negative' else 0,
                'model': 'Basic Keywords',
                'symbol': symbol
            }
            
        except Exception as e:
            logger.error(f"Basic sentiment analysis failed: {e}")
            return {
                'sentiment': 'neutral',
                'confidence': 0.5,
                'sentiment_score': 0,
                'error': str(e)
            }

    async def analyze_news_sentiment(self, news_articles: List[Dict[str, Any]], 
                                   symbol: str = None) -> Dict[str, Any]:
        """Analyze sentiment of multiple news articles"""
        try:
            if not news_articles:
                return {'overall_sentiment': 'neutral', 'confidence': 0.5}
            
            sentiments = []
            for article in news_articles[:10]:  # Limit to 10 articles
                title = article.get('title', '')
                content = article.get('content', article.get('description', ''))
                text = f"{title} {content}"
                
                sentiment_result = await self.analyze_sentiment(text, symbol)
                sentiments.append(sentiment_result)
            
            # Calculate overall sentiment
            avg_score = np.mean([s['sentiment_score'] for s in sentiments])
            avg_confidence = np.mean([s['confidence'] for s in sentiments])
            
            if avg_score > 0.1:
                overall_sentiment = 'positive'
            elif avg_score < -0.1:
                overall_sentiment = 'negative'
            else:
                overall_sentiment = 'neutral'
            
            return {
                'symbol': symbol,
                'overall_sentiment': overall_sentiment,
                'sentiment_score': avg_score,
                'confidence': avg_confidence,
                'articles_analyzed': len(sentiments),
                'individual_sentiments': sentiments,
                'summary': f"Analyzed {len(sentiments)} articles with {overall_sentiment} sentiment (score: {avg_score:.2f})"
            }
            
        except Exception as e:
            logger.error(f"News sentiment analysis failed: {e}")
            return {'error': str(e)}


# ============================================================================
# OPTIONS FLOW ANALYZER
# ============================================================================

class AtlasOptionsFlowAnalyzer:
    """Options flow and unusual activity analyzer"""
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.flow_data = {}
        self.unusual_activity = {}
        
        logger.info(f"[DATA] Options Flow Analyzer initialized - enabled: True, ML: {TORCH_AVAILABLE}")

    async def initialize(self):
        """Initialize options flow analyzer"""
        try:
            self.status = EngineStatus.INITIALIZING
            
            # Initialize flow tracking
            self.flow_thresholds = {
                'volume_threshold': 1000,  # Unusual if > 1000 contracts
                'oi_ratio_threshold': 2.0,  # Unusual if volume > 2x open interest
                'price_threshold': 0.05  # Unusual if > 5% price movement
            }
            
            self.status = EngineStatus.ACTIVE
            logger.info("[OK] Options Flow Analyzer ready")
            
        except Exception as e:
            logger.error(f"Options Flow Analyzer initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def analyze_options_flow(self, symbol: str, timeframe: str = '1d') -> Dict[str, Any]:
        """Analyze options flow for unusual activity"""
        try:
            # Simulate options flow analysis
            # In production, this would connect to options data feed
            
            flow_data = {
                'symbol': symbol,
                'timeframe': timeframe,
                'call_volume': np.random.randint(500, 5000),
                'put_volume': np.random.randint(300, 3000),
                'call_oi': np.random.randint(1000, 10000),
                'put_oi': np.random.randint(800, 8000),
                'unusual_activity': []
            }
            
            # Calculate metrics
            total_volume = flow_data['call_volume'] + flow_data['put_volume']
            call_put_ratio = flow_data['call_volume'] / flow_data['put_volume']
            
            # Detect unusual activity
            if flow_data['call_volume'] > self.flow_thresholds['volume_threshold']:
                flow_data['unusual_activity'].append({
                    'type': 'high_call_volume',
                    'description': f"Unusually high call volume: {flow_data['call_volume']} contracts",
                    'significance': 'bullish'
                })
            
            if flow_data['put_volume'] > self.flow_thresholds['volume_threshold']:
                flow_data['unusual_activity'].append({
                    'type': 'high_put_volume',
                    'description': f"Unusually high put volume: {flow_data['put_volume']} contracts",
                    'significance': 'bearish'
                })
            
            # Add derived metrics
            flow_data.update({
                'total_volume': total_volume,
                'call_put_ratio': round(call_put_ratio, 2),
                'sentiment': 'bullish' if call_put_ratio > 1.5 else 'bearish' if call_put_ratio < 0.7 else 'neutral',
                'activity_level': 'high' if total_volume > 2000 else 'normal',
                'analysis_timestamp': datetime.now().isoformat()
            })
            
            return flow_data
            
        except Exception as e:
            logger.error(f"Options flow analysis failed for {symbol}: {e}")
            return {'error': str(e)}

    async def detect_unusual_options_activity(self, symbols: List[str]) -> Dict[str, Any]:
        """Detect unusual options activity across multiple symbols"""
        try:
            unusual_activities = []
            
            for symbol in symbols[:10]:  # Limit to 10 symbols
                flow_data = await self.analyze_options_flow(symbol)
                
                if flow_data.get('unusual_activity'):
                    unusual_activities.append({
                        'symbol': symbol,
                        'activities': flow_data['unusual_activity'],
                        'call_put_ratio': flow_data.get('call_put_ratio', 1.0),
                        'total_volume': flow_data.get('total_volume', 0)
                    })
            
            return {
                'scan_timestamp': datetime.now().isoformat(),
                'symbols_scanned': len(symbols),
                'unusual_activities_found': len(unusual_activities),
                'activities': unusual_activities,
                'summary': f"Found {len(unusual_activities)} symbols with unusual options activity"
            }
            
        except Exception as e:
            logger.error(f"Unusual activity detection failed: {e}")
            return {'error': str(e)}


# ============================================================================
# ML ANALYTICS ORCHESTRATOR
# ============================================================================

class AtlasMLAnalyticsOrchestrator:
    """Main ML analytics orchestrator"""
    
    def __init__(self):
        self.ml_predictor = AtlasMLPredictor()
        self.sentiment_analyzer = AtlasSentimentAnalyzer()
        self.advanced_ml_engine = AtlasAdvancedMLEngine()  # v5.0 Enhancement
        self.options_flow_analyzer = AtlasOptionsFlowAnalyzer()
        self.status = EngineStatus.INITIALIZING

        # Grok integration for ML optimization
        self.grok_integration_available = GROK_INTEGRATION_AVAILABLE
        self.grok_engine = None
        self.optimized_models = {}

        logger.info(f"[ORCHESTRATOR] ML Analytics Orchestrator initialized - Grok: {self.grok_integration_available}")

    async def initialize(self):
        """Initialize all ML analytics components"""
        try:
            await self.ml_predictor.initialize()
            await self.sentiment_analyzer.initialize()
            await self.options_flow_analyzer.initialize()
            await self.advanced_ml_engine.initialize()  # v5.0 Enhancement

            # Initialize Grok integration for ML optimization
            if self.grok_integration_available:
                try:
                    self.grok_engine = AtlasGrokIntegrationEngine()
                    grok_success = await self.grok_engine.initialize()
                    if grok_success:
                        logger.info("[OK] Grok integration initialized for ML optimization")
                    else:
                        logger.warning("[FALLBACK] Grok API not available - ML code optimization disabled")
                except Exception as e:
                    logger.error(f"Grok integration initialization failed: {e}")
                    self.grok_engine = None
            else:
                logger.warning("[FALLBACK] Grok integration not available")

            self.status = EngineStatus.ACTIVE
            logger.info("[OK] ML Analytics Orchestrator fully initialized")

        except Exception as e:
            logger.error(f"ML Analytics Orchestrator initialization failed: {e}")
            self.status = EngineStatus.FAILED
            raise

    async def optimize_ml_model_with_grok(self, model_name: str, model_code: str,
                                        optimization_target: str = "performance") -> Dict[str, Any]:
        """Optimize ML model code using Grok's code generation capabilities"""
        try:
            if not self.grok_engine:
                return {
                    'success': False,
                    'error': 'Grok integration not available',
                    'original_code': model_code,
                    'optimized_code': None
                }

            # Use Grok code optimizer
            grok_response = await self.grok_engine.code_optimizer.optimize_ml_code(
                model_code, optimization_target
            )

            if grok_response.success:
                # Store optimized model
                self.optimized_models[model_name] = {
                    'original_code': model_code,
                    'optimized_code': grok_response.content,
                    'optimization_target': optimization_target,
                    'confidence': grok_response.confidence,
                    'timestamp': datetime.now().isoformat()
                }

                logger.info(f"[GROK] Successfully optimized {model_name} for {optimization_target}")

                return {
                    'success': True,
                    'model_name': model_name,
                    'original_code': model_code,
                    'optimized_code': grok_response.content,
                    'optimization_target': optimization_target,
                    'confidence': grok_response.confidence,
                    'processing_time': grok_response.processing_time,
                    'tokens_used': grok_response.tokens_used,
                    'recommendations': self._extract_optimization_recommendations(grok_response.content)
                }
            else:
                logger.error(f"Grok optimization failed for {model_name}: {grok_response.error_message}")
                return {
                    'success': False,
                    'error': grok_response.error_message,
                    'model_name': model_name,
                    'original_code': model_code,
                    'optimized_code': None
                }

        except Exception as e:
            logger.error(f"ML model optimization failed for {model_name}: {e}")
            return {
                'success': False,
                'error': str(e),
                'model_name': model_name,
                'original_code': model_code,
                'optimized_code': None
            }

    def _extract_optimization_recommendations(self, optimized_code: str) -> List[str]:
        """Extract optimization recommendations from Grok response"""
        try:
            recommendations = []

            # Look for common optimization patterns in the response
            lines = optimized_code.split('\n')
            for line in lines:
                line_lower = line.lower().strip()
                if any(keyword in line_lower for keyword in ['# optimization:', '# improved:', '# performance:', '# faster:']):
                    recommendations.append(line.strip())
                elif line_lower.startswith('# ') and any(word in line_lower for word in ['better', 'faster', 'efficient', 'optimized']):
                    recommendations.append(line.strip())

            return recommendations[:10]  # Limit to top 10 recommendations

        except Exception as e:
            logger.error(f"Recommendation extraction failed: {e}")
            return ["Optimization recommendations extraction failed"]

    async def optimize_existing_models(self) -> Dict[str, Any]:
        """Optimize all existing ML models using Grok"""
        try:
            optimization_results = {}

            # Define models to optimize with their current implementations
            models_to_optimize = {
                'lstm_predictor': self._get_lstm_model_code(),
                'transformer_predictor': self._get_transformer_model_code(),
                'ensemble_predictor': self._get_ensemble_model_code(),
                'sentiment_classifier': self._get_sentiment_model_code()
            }

            for model_name, model_code in models_to_optimize.items():
                if model_code:
                    result = await self.optimize_ml_model_with_grok(
                        model_name, model_code, "performance_and_accuracy"
                    )
                    optimization_results[model_name] = result

                    # Add small delay to respect rate limits
                    await asyncio.sleep(1)

            return {
                'optimization_results': optimization_results,
                'total_models': len(models_to_optimize),
                'successful_optimizations': sum(1 for r in optimization_results.values() if r.get('success', False)),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Batch model optimization failed: {e}")
            return {'error': str(e)}

    def _get_lstm_model_code(self) -> str:
        """Get LSTM model code for optimization"""
        return '''
import torch
import torch.nn as nn

class LSTMPredictor(nn.Module):
    def __init__(self, input_size=10, hidden_size=50, num_layers=2, output_size=1):
        super(LSTMPredictor, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)
        self.fc = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)

        out, _ = self.lstm(x, (h0, c0))
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)
        return out
'''

    def _get_transformer_model_code(self) -> str:
        """Get Transformer model code for optimization"""
        return '''
import torch
import torch.nn as nn
import math

class TransformerPredictor(nn.Module):
    def __init__(self, input_size=10, d_model=64, nhead=8, num_layers=3, output_size=1):
        super(TransformerPredictor, self).__init__()
        self.d_model = d_model
        self.input_projection = nn.Linear(input_size, d_model)
        self.pos_encoder = PositionalEncoding(d_model)

        encoder_layer = nn.TransformerEncoderLayer(d_model, nhead, batch_first=True)
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers)
        self.fc = nn.Linear(d_model, output_size)

    def forward(self, x):
        x = self.input_projection(x) * math.sqrt(self.d_model)
        x = self.pos_encoder(x)
        x = self.transformer(x)
        x = x.mean(dim=1)  # Global average pooling
        return self.fc(x)

class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super(PositionalEncoding, self).__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.unsqueeze(0))

    def forward(self, x):
        return x + self.pe[:, :x.size(1)]
'''

    def _get_ensemble_model_code(self) -> str:
        """Get Ensemble model code for optimization"""
        return '''
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
import numpy as np

class EnsemblePredictor:
    def __init__(self):
        self.models = {
            'rf': RandomForestRegressor(n_estimators=100, random_state=42),
            'gb': GradientBoostingRegressor(n_estimators=100, random_state=42),
            'lr': LinearRegression()
        }
        self.weights = {'rf': 0.4, 'gb': 0.4, 'lr': 0.2}

    def fit(self, X, y):
        for name, model in self.models.items():
            model.fit(X, y)

    def predict(self, X):
        predictions = {}
        for name, model in self.models.items():
            predictions[name] = model.predict(X)

        # Weighted average
        final_pred = np.zeros(len(X))
        for name, pred in predictions.items():
            final_pred += self.weights[name] * pred

        return final_pred
'''

    def _get_sentiment_model_code(self) -> str:
        """Get Sentiment model code for optimization"""
        return '''
import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer

class SentimentClassifier(nn.Module):
    def __init__(self, model_name='distilbert-base-uncased', num_classes=3):
        super(SentimentClassifier, self).__init__()
        self.bert = AutoModel.from_pretrained(model_name)
        self.dropout = nn.Dropout(0.3)
        self.classifier = nn.Linear(self.bert.config.hidden_size, num_classes)

    def forward(self, input_ids, attention_mask):
        outputs = self.bert(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.pooler_output
        output = self.dropout(pooled_output)
        return self.classifier(output)
'''

    async def comprehensive_analysis(self, symbol: str, news_articles: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Perform comprehensive ML analysis"""
        try:
            # Run all analyses in parallel
            tasks = [
                self.ml_predictor.predict_price(symbol),
                self.sentiment_analyzer.analyze_news_sentiment(news_articles or [], symbol),
                self.options_flow_analyzer.analyze_options_flow(symbol)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            return {
                'symbol': symbol,
                'analysis_timestamp': datetime.now().isoformat(),
                'price_prediction': results[0] if not isinstance(results[0], Exception) else {'error': str(results[0])},
                'sentiment_analysis': results[1] if not isinstance(results[1], Exception) else {'error': str(results[1])},
                'options_flow': results[2] if not isinstance(results[2], Exception) else {'error': str(results[2])},
                'overall_signal': self._generate_overall_signal(results)
            }
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed for {symbol}: {e}")
            return {'error': str(e)}

    def _generate_overall_signal(self, results: List[Any]) -> Dict[str, Any]:
        """Generate overall trading signal from all analyses"""
        try:
            signals = []
            
            # Price prediction signal
            if not isinstance(results[0], Exception) and 'direction' in results[0]:
                direction = results[0]['direction']
                if direction == 'bullish':
                    signals.append(1)
                elif direction == 'bearish':
                    signals.append(-1)
                else:
                    signals.append(0)
            
            # Sentiment signal
            if not isinstance(results[1], Exception) and 'overall_sentiment' in results[1]:
                sentiment = results[1]['overall_sentiment']
                if sentiment == 'positive':
                    signals.append(1)
                elif sentiment == 'negative':
                    signals.append(-1)
                else:
                    signals.append(0)
            
            # Options flow signal
            if not isinstance(results[2], Exception) and 'sentiment' in results[2]:
                options_sentiment = results[2]['sentiment']
                if options_sentiment == 'bullish':
                    signals.append(1)
                elif options_sentiment == 'bearish':
                    signals.append(-1)
                else:
                    signals.append(0)
            
            # Calculate overall signal
            if signals:
                avg_signal = np.mean(signals)
                if avg_signal > 0.3:
                    overall_signal = 'bullish'
                elif avg_signal < -0.3:
                    overall_signal = 'bearish'
                else:
                    overall_signal = 'neutral'
            else:
                overall_signal = 'neutral'
            
            return {
                'overall_signal': overall_signal,
                'signal_strength': abs(np.mean(signals)) if signals else 0,
                'signals_count': len(signals),
                'confidence': min(0.8, abs(np.mean(signals))) if signals else 0.5
            }
            
        except Exception as e:
            logger.error(f"Overall signal generation failed: {e}")
            return {'overall_signal': 'neutral', 'confidence': 0.5}

    # ============================================================================
    # ADVANCED ML METHODS (v5.0 Enhancement)
    # ============================================================================

    async def advanced_prediction(self, symbol: str, features: np.ndarray) -> Dict[str, Any]:
        """Make advanced prediction using neural networks and ensemble models"""
        try:
            # Get hybrid prediction from advanced ML engine
            hybrid_result = await self.advanced_ml_engine.hybrid_prediction(features)

            if 'error' in hybrid_result:
                # Fallback to traditional ML prediction
                traditional_result = await self.ml_predictor.predict_price(symbol)
                return {
                    'prediction_type': 'traditional_fallback',
                    'result': traditional_result,
                    'timestamp': datetime.now().isoformat()
                }

            return {
                'prediction_type': 'advanced_hybrid',
                'result': hybrid_result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Advanced prediction failed for {symbol}: {e}")
            return {'error': str(e)}

    async def lstm_prediction(self, symbol: str, features: np.ndarray) -> Dict[str, Any]:
        """Make LSTM-based prediction"""
        try:
            result = await self.advanced_ml_engine.predict_with_lstm(features)
            return {
                'symbol': symbol,
                'prediction_type': 'lstm',
                'result': result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"LSTM prediction failed for {symbol}: {e}")
            return {'error': str(e)}

    async def transformer_prediction(self, symbol: str, features: np.ndarray) -> Dict[str, Any]:
        """Make Transformer-based prediction"""
        try:
            result = await self.advanced_ml_engine.predict_with_transformer(features)
            return {
                'symbol': symbol,
                'prediction_type': 'transformer',
                'result': result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Transformer prediction failed for {symbol}: {e}")
            return {'error': str(e)}

    async def ensemble_prediction(self, symbol: str, features: np.ndarray) -> Dict[str, Any]:
        """Make ensemble-based prediction"""
        try:
            result = await self.advanced_ml_engine.predict_with_ensemble(features)
            return {
                'symbol': symbol,
                'prediction_type': 'ensemble',
                'result': result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Ensemble prediction failed for {symbol}: {e}")
            return {'error': str(e)}

    async def update_models_online(self, features: np.ndarray, target: float) -> Dict[str, Any]:
        """Update models with new data for online learning"""
        try:
            result = await self.advanced_ml_engine.online_learning_update(features, target)
            return {
                'online_learning_update': result,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Online learning update failed: {e}")
            return {'error': str(e)}

    def get_advanced_ml_status(self) -> Dict[str, Any]:
        """Get status of advanced ML components"""
        try:
            return {
                'advanced_ml_engine': self.advanced_ml_engine.get_engine_status(),
                'model_performance': self.advanced_ml_engine.get_model_performance(),
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Advanced ML status retrieval failed: {e}")
            return {'error': str(e)}

    def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive status including Grok optimization"""
        try:
            status = {
                'orchestrator_status': self.status.value,
                'grok_integration_available': self.grok_integration_available,
                'components': {
                    'ml_predictor': self.ml_predictor.status.value if hasattr(self.ml_predictor, 'status') else 'unknown',
                    'sentiment_analyzer': self.sentiment_analyzer.status.value if hasattr(self.sentiment_analyzer, 'status') else 'unknown',
                    'options_flow_analyzer': self.options_flow_analyzer.status.value if hasattr(self.options_flow_analyzer, 'status') else 'unknown',
                    'advanced_ml_engine': self.advanced_ml_engine.get_engine_status()
                },
                'grok_optimization': {
                    'optimized_models_count': len(self.optimized_models),
                    'optimized_models': list(self.optimized_models.keys()),
                    'grok_engine_available': self.grok_engine is not None
                },
                'timestamp': datetime.now().isoformat()
            }

            # Add Grok engine status if available
            if self.grok_engine:
                status['grok_optimization']['grok_engine_status'] = self.grok_engine.get_engine_status()

            return status

        except Exception as e:
            logger.error(f"Comprehensive status retrieval failed: {e}")
            return {'error': str(e)}

    async def generate_optimization_report(self) -> Dict[str, Any]:
        """Generate comprehensive optimization report"""
        try:
            report = {
                'optimization_summary': {
                    'total_models_optimized': len(self.optimized_models),
                    'optimization_targets': {},
                    'average_confidence': 0.0,
                    'recommendations_count': 0
                },
                'model_details': {},
                'performance_improvements': {},
                'timestamp': datetime.now().isoformat()
            }

            if self.optimized_models:
                total_confidence = 0
                total_recommendations = 0

                for model_name, optimization_data in self.optimized_models.items():
                    target = optimization_data.get('optimization_target', 'unknown')
                    confidence = optimization_data.get('confidence', 0.0)

                    # Track optimization targets
                    if target not in report['optimization_summary']['optimization_targets']:
                        report['optimization_summary']['optimization_targets'][target] = 0
                    report['optimization_summary']['optimization_targets'][target] += 1

                    # Add to totals
                    total_confidence += confidence

                    # Model details
                    report['model_details'][model_name] = {
                        'optimization_target': target,
                        'confidence': confidence,
                        'timestamp': optimization_data.get('timestamp', 'unknown'),
                        'has_optimized_code': 'optimized_code' in optimization_data
                    }

                # Calculate averages
                report['optimization_summary']['average_confidence'] = total_confidence / len(self.optimized_models)

            return report

        except Exception as e:
            logger.error(f"Optimization report generation failed: {e}")
            return {'error': str(e)}


# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    "AtlasMLPredictor",
    "AtlasSentimentAnalyzer",
    "AtlasOptionsFlowAnalyzer",
    "AtlasMLAnalyticsOrchestrator",
    "AtlasAdvancedMLEngine",  # v5.0 Enhancement
    "AtlasLSTMModel",         # v5.0 Enhancement
    "AtlasTransformerModel"   # v5.0 Enhancement
]
