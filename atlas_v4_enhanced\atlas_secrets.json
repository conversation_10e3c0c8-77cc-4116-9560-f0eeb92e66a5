{"secrets": {"fmp_api_key": "Z0FBQUFBQm9nVW93SWZGLWh0QnZOYnh2RzFtdDhibW1Nd2NaZjVvZmg5ay1TTjcySm1GTWx1bWdCZXBrbldmbG00ZG5LMkZHeDhYbVYwUWVBblNGX3lqNENrLW9VXzVVaU5FTmtVRnVQMWlOT3IyZjI5YVIyTDNNWThxajBkbVRvMUlSeFIxZk8tWHY=", "alpaca_api_key": "Z0FBQUFBQm9nVW93Vi02SHVGb0ZIbFVSTndZQmxiNjR1RUR6WDI0N096WGhCWWh4bXhTbEJPa0NzcGdQRGV1NzhKTFd0ZV9vTE90bmFPaEFMSVZiazkzZ21GZ0dGYkVlY3pnZjZ6dUYyWnhHVkJ4OXNqdEFQOWc9", "alpaca_secret_key": "Z0FBQUFBQm9nVW93d2c2NVJPU000M05EYll6c2c3VVZrOUpxWnFUcy1uVjZYWk1ORlhIUmFiN1JMX2w1UHk5WFVQejdYdzYySno5ak51WU00MXJ6TG13UjVzSHBqUG04YzN4T2xXa0FZSFkzbDZoNWxjd3d4Q2xNVEFFOFNZaW5mMTFncmtUVHFWVUY=", "grok_api_key": "Z0FBQUFBQm9nVW93OE5nQUVUb2haMkoxOFhhZEZDLW5IZGhHak40NnMxY3BiRmtwc0c1bkpvS1dBT2E3OW95RzlpX2FDeTBzNHpmN0FZUE9QdzVneWdsRTFYNGp3bHhiWFozakRQeS1XUTFOc1ZCbnB1MkR5UTVjS0lfLUZnOGlUWkx1eG1ERU9oM1ZXYXMtbDBHbm43dWRWMUFBYmdlUUd3M0hqS2JWWFlzYVpEYUF0LUVkUkcxSnNGNWZweGk0dTlBZm82aHRVZm95", "openai_api_key": "Z0FBQUFBQm9nVW93OGVPZ3o4MlZocUhqdE04bkdaQ0NTa1pYYzhUczdqVlFBU2RSS2N3Z1pjRFY5UDdwU29wX3BfYVBDWDEwT0s3c0FjUjVrbGZtUk1NYXVJc2wyWFpsa2pyamktaXVkaXNEUk1QU3BQejNXSThZdHNEV0hqN2VxcnduWnExeXBpOXhXYXIzZlNRMUVzVFpFZDQ4VzJRVFMweXNZRTZLUUpKVlZKZ0ZWREF6WnN6U1QzT0YtRGFWRjdoYUJxdDBrdWl3SVFvcDQyaHBaV2Z4eDVBX1ktWVJ4VjdBOFVMVFR3dHhHYWlKUWRoOUxFX0gtd2RpMUxkbXhONXllRmwzS2hzY0YxZ3I2aFZfOWZFQk1FZXNtUVhmYlNUZEgxQVBhRS1zc2Noak1obU1hRFk9"}, "metadata": {"fmp_api_key": {"name": "fmp_api_key", "created_at": "2025-07-23T15:18:08.307218", "last_accessed": "2025-07-23T15:46:40.129016", "access_count": 2, "encrypted": true, "source": "setup"}, "alpaca_api_key": {"name": "alpaca_api_key", "created_at": "2025-07-23T15:18:08.309775", "last_accessed": "2025-07-23T15:46:40.129622", "access_count": 2, "encrypted": true, "source": "setup"}, "alpaca_secret_key": {"name": "alpaca_secret_key", "created_at": "2025-07-23T15:18:08.310325", "last_accessed": "2025-07-23T15:46:40.130171", "access_count": 2, "encrypted": true, "source": "setup"}, "grok_api_key": {"name": "grok_api_key", "created_at": "2025-07-23T15:18:08.310704", "last_accessed": "2025-07-23T15:46:40.130642", "access_count": 2, "encrypted": true, "source": "setup"}, "openai_api_key": {"name": "openai_api_key", "created_at": "2025-07-23T15:46:40.131346", "last_accessed": "2025-07-23T15:46:40.131346", "access_count": 1, "encrypted": true, "source": "environment"}}, "version": "1.0", "created_at": "2025-07-23T15:46:40.131465"}