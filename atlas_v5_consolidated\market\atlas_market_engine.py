"""
A.T.L.A.S. Consolidated Market Engine
Combines all market data and analysis functionality:
- Market data processing and intelligence
- FMP API integration and data validation
- Stock intelligence hub with comprehensive analysis
- Real-time data handling and caching
- Data quality monitoring and validation
- Performance optimization and error handling
"""

import asyncio
import logging
import aiohttp
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import time

# Core imports
from models import Quote, EngineStatus, TechnicalIndicators, MarketContext
from config import get_api_config, settings

logger = logging.getLogger(__name__)

@dataclass
class DataQualityMetrics:
    """Data quality assessment metrics"""
    quality_score: float
    issues: List[str]
    source: str
    timestamp: datetime
    is_valid: bool

class AtlasMarketEngine:
    """
    Consolidated Market Engine
    Combines all market data processing, intelligence, and validation functionality
    """
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.logger = logging.getLogger(__name__)
        
        # API Configuration
        self.fmp_api_key = None
        self.api_config = {}
        
        # Data caching and performance
        self.quote_cache = {}
        self.news_cache = {}
        self.analysis_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Data quality and validation
        self.data_validation_enabled = True
        self.legitimate_data_sources = {'fmp', 'alpaca', 'yfinance'}
        self.quality_metrics = {}
        
        # Trading safety system
        self.trading_halt_active = False
        self.data_failure_count = 0
        self.max_data_failures = 5
        self.last_successful_data_time = datetime.now()
        
        # Performance tracking
        self.request_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "cache_hits": 0,
            "avg_response_time": 0.0
        }
        
        # Stock Intelligence Hub components
        self.technical_analyzer = None
        self.sentiment_analyzer = None
        self.prediction_engine = None
        self.market_intelligence = None
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all market engine components"""
        try:
            # Load API configuration
            self._load_api_config()
            
            # Initialize intelligence modules
            self._initialize_intelligence_hub()
            
            # Initialize data validation
            self._initialize_data_validation()
            
            # Initialize caching system
            self._initialize_caching()
            
            self.status = EngineStatus.RUNNING
            self.logger.info("✅ [MARKET_ENGINE] All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ [MARKET_ENGINE] Initialization failed: {e}")
            self.status = EngineStatus.ERROR
            raise
    
    def _load_api_config(self):
        """Load API configuration"""
        try:
            self.api_config = get_api_config()
            self.fmp_api_key = self.api_config.get('fmp_api_key')
            
            if not self.fmp_api_key:
                self.logger.warning("⚠️ [CONFIG] FMP API key not found - market data may be limited")
            else:
                self.logger.info("🔑 [CONFIG] FMP API key loaded successfully")
                
        except Exception as e:
            self.logger.error(f"❌ [CONFIG] Failed to load API configuration: {e}")
            raise
    
    def _initialize_intelligence_hub(self):
        """Initialize Stock Intelligence Hub components"""
        try:
            # Initialize analysis modules (simplified for consolidation)
            self.technical_analyzer = TechnicalAnalysisModule()
            self.sentiment_analyzer = SentimentAnalysisModule()
            self.prediction_engine = PredictionEngineModule()
            self.market_intelligence = MarketIntelligenceModule()
            
            self.logger.info("🧠 [INTELLIGENCE] Stock Intelligence Hub initialized")
            
        except Exception as e:
            self.logger.error(f"❌ [INTELLIGENCE] Failed to initialize intelligence hub: {e}")
            raise
    
    def _initialize_data_validation(self):
        """Initialize data validation systems"""
        try:
            self.validation_rules = {
                "price_range": (0.01, 10000.0),  # Valid price range
                "volume_range": (0, 1000000000),  # Valid volume range
                "max_data_age": 3600,  # Max 1 hour old data
                "required_fields": ["price", "volume", "timestamp"]
            }
            
            self.logger.info("✅ [VALIDATION] Data validation systems initialized")
            
        except Exception as e:
            self.logger.error(f"❌ [VALIDATION] Failed to initialize validation: {e}")
            raise
    
    def _initialize_caching(self):
        """Initialize caching systems"""
        try:
            # Clear old cache entries periodically
            self._last_cache_cleanup = datetime.now()
            self._cache_cleanup_interval = 600  # 10 minutes
            
            self.logger.info("💾 [CACHE] Caching systems initialized")
            
        except Exception as e:
            self.logger.error(f"❌ [CACHE] Failed to initialize caching: {e}")
            raise

    # ============================================================================
    # CORE MARKET DATA FUNCTIONALITY
    # ============================================================================
    
    async def get_quote(self, symbol: str) -> Optional[Quote]:
        """Get real-time quote for symbol with caching and validation"""
        try:
            start_time = time.time()
            self.request_stats["total_requests"] += 1
            
            # Check cache first
            cache_key = f"quote_{symbol}"
            if self._is_cache_valid(cache_key):
                self.request_stats["cache_hits"] += 1
                cached_data = self.quote_cache[cache_key]["data"]
                return Quote(**cached_data)
            
            # Fetch fresh data from FMP API
            quote_data = await self._fetch_fmp_quote(symbol)
            
            if quote_data:
                # Validate data quality
                is_valid, quality_metrics = await self._validate_market_data(symbol, quote_data, "fmp")
                
                if is_valid:
                    # Create Quote object
                    quote = Quote(
                        symbol=symbol,
                        price=quote_data.get("price", 0.0),
                        change=quote_data.get("change", 0.0),
                        change_percent=quote_data.get("changesPercentage", 0.0),
                        volume=quote_data.get("volume", 0),
                        timestamp=datetime.now()
                    )
                    
                    # Cache the result
                    self._cache_data(cache_key, quote_data)
                    
                    # Update stats
                    self.request_stats["successful_requests"] += 1
                    response_time = time.time() - start_time
                    self._update_response_time(response_time)
                    
                    self.logger.info(f"📈 [QUOTE] Retrieved quote for {symbol}: ${quote.price:.2f}")
                    return quote
                else:
                    self.logger.warning(f"⚠️ [QUALITY] Data quality issues for {symbol}: {quality_metrics.issues}")
            
            # Data fetch failed
            self.request_stats["failed_requests"] += 1
            self.data_failure_count += 1
            
            # Check if we should halt trading
            if self.data_failure_count >= self.max_data_failures:
                self.trading_halt_active = True
                self.logger.error(f"🚨 [HALT] Trading halted due to {self.data_failure_count} consecutive data failures")
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ [QUOTE] Failed to get quote for {symbol}: {e}")
            self.request_stats["failed_requests"] += 1
            return None
    
    async def _fetch_fmp_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch quote data from FMP API"""
        try:
            if not self.fmp_api_key:
                self.logger.error("❌ [FMP] No API key available")
                return None
            
            url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
            params = {"apikey": self.fmp_api_key}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and len(data) > 0:
                            return data[0]  # FMP returns array with single quote
                    else:
                        self.logger.error(f"❌ [FMP] API error {response.status} for {symbol}")
            
            return None
            
        except asyncio.TimeoutError:
            self.logger.error(f"⏰ [FMP] Timeout fetching quote for {symbol}")
            return None
        except Exception as e:
            self.logger.error(f"❌ [FMP] Error fetching quote for {symbol}: {e}")
            return None
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive market data for symbol"""
        try:
            self.logger.info(f"📊 [MARKET_DATA] Compiling comprehensive data for {symbol}")
            
            # Get quote data
            quote = await self.get_quote(symbol)
            quote_data = {
                "price": quote.price if quote else 0.0,
                "change": quote.change if quote else 0.0,
                "change_percent": quote.change_percent if quote else 0.0,
                "volume": quote.volume if quote else 0,
                "timestamp": quote.timestamp.isoformat() if quote else datetime.now().isoformat()
            }
            
            # Get news data
            news_data = await self.get_market_news(symbol, limit=5)
            
            # Get comprehensive analysis
            analysis_data = await self.analyze_stock_comprehensive(symbol)
            
            # Bundle all data together
            market_data = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "quote": quote_data,
                "news": news_data,
                "analysis": analysis_data,
                "status": "success" if quote else "limited_data"
            }
            
            self.logger.info(f"✅ [MARKET_DATA] Compiled comprehensive data for {symbol}")
            return market_data
            
        except Exception as e:
            self.logger.error(f"❌ [MARKET_DATA] Error compiling data for {symbol}: {e}")
            return {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "status": "error"
            }
    
    async def get_market_news(self, symbol: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get market news for symbol"""
        try:
            # Check cache first
            cache_key = f"news_{symbol}_{limit}"
            if self._is_cache_valid(cache_key):
                return self.news_cache[cache_key]["data"]
            
            # Fetch from FMP API
            news_data = await self._fetch_fmp_news(symbol, limit)
            
            if news_data:
                # Cache the result
                self._cache_data(cache_key, news_data, cache_type="news")
                return news_data
            
            return []
            
        except Exception as e:
            self.logger.error(f"❌ [NEWS] Failed to get news for {symbol}: {e}")
            return []
    
    async def _fetch_fmp_news(self, symbol: str, limit: int) -> List[Dict[str, Any]]:
        """Fetch news from FMP API"""
        try:
            if not self.fmp_api_key:
                return []
            
            url = f"https://financialmodelingprep.com/api/v3/stock_news"
            params = {
                "tickers": symbol,
                "limit": limit,
                "apikey": self.fmp_api_key
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params, timeout=15) as response:
                    if response.status == 200:
                        data = await response.json()
                        return data if isinstance(data, list) else []
                    else:
                        self.logger.error(f"❌ [FMP_NEWS] API error {response.status} for {symbol}")
            
            return []
            
        except Exception as e:
            self.logger.error(f"❌ [FMP_NEWS] Error fetching news for {symbol}: {e}")
            return []

    # ============================================================================
    # STOCK INTELLIGENCE HUB
    # ============================================================================
    
    async def analyze_stock_comprehensive(self, symbol: str) -> Dict[str, Any]:
        """Perform comprehensive stock analysis combining all intelligence modules"""
        try:
            # Check cache first
            cache_key = f"comprehensive_{symbol}"
            if self._is_cache_valid(cache_key):
                return self.analysis_cache[cache_key]["data"]
            
            self.logger.info(f"🔍 [ANALYSIS] Performing comprehensive analysis for {symbol}")
            
            # Parallel execution of all analysis modules
            analysis_tasks = [
                self._technical_analysis(symbol),
                self._sentiment_analysis(symbol),
                self._prediction_analysis(symbol),
                self._market_intelligence_analysis(symbol)
            ]
            
            # Execute all analyses concurrently
            results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
            
            # Combine results
            comprehensive_analysis = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "technical_analysis": results[0] if not isinstance(results[0], Exception) else {"error": str(results[0])},
                "sentiment_analysis": results[1] if not isinstance(results[1], Exception) else {"error": str(results[1])},
                "prediction_analysis": results[2] if not isinstance(results[2], Exception) else {"error": str(results[2])},
                "market_intelligence": results[3] if not isinstance(results[3], Exception) else {"error": str(results[3])},
                "overall_score": self._calculate_overall_score(results),
                "recommendation": self._generate_recommendation(results)
            }
            
            # Cache the result
            self._cache_data(cache_key, comprehensive_analysis, cache_type="analysis")
            
            self.logger.info(f"✅ [ANALYSIS] Comprehensive analysis completed for {symbol}")
            return comprehensive_analysis
            
        except Exception as e:
            self.logger.error(f"❌ [ANALYSIS] Comprehensive analysis failed for {symbol}: {e}")
            return {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "status": "failed"
            }

    async def _technical_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform technical analysis"""
        try:
            # Simulate technical analysis (in real implementation, would use actual indicators)
            return {
                "trend": "bullish",
                "support_level": 145.0,
                "resistance_level": 155.0,
                "rsi": 65.2,
                "macd_signal": "buy",
                "moving_averages": {
                    "sma_20": 148.5,
                    "sma_50": 146.2,
                    "ema_12": 149.1
                },
                "volume_analysis": "above_average",
                "technical_score": 7.5,
                "confidence": 0.78
            }

        except Exception as e:
            self.logger.error(f"❌ [TECHNICAL] Technical analysis failed for {symbol}: {e}")
            return {"error": str(e)}

    async def _sentiment_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform sentiment analysis"""
        try:
            # Simulate sentiment analysis (in real implementation, would analyze news/social media)
            return {
                "overall_sentiment": "positive",
                "sentiment_score": 0.72,
                "news_sentiment": "bullish",
                "social_sentiment": "neutral",
                "analyst_ratings": {
                    "buy": 8,
                    "hold": 3,
                    "sell": 1
                },
                "sentiment_trend": "improving",
                "confidence": 0.65
            }

        except Exception as e:
            self.logger.error(f"❌ [SENTIMENT] Sentiment analysis failed for {symbol}: {e}")
            return {"error": str(e)}

    async def _prediction_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform prediction analysis"""
        try:
            # Simulate ML prediction (in real implementation, would use trained models)
            return {
                "price_prediction_1d": 152.3,
                "price_prediction_7d": 158.7,
                "price_prediction_30d": 165.2,
                "direction_probability": {
                    "up": 0.68,
                    "down": 0.32
                },
                "volatility_forecast": "moderate",
                "prediction_confidence": 0.71,
                "model_accuracy": 0.74
            }

        except Exception as e:
            self.logger.error(f"❌ [PREDICTION] Prediction analysis failed for {symbol}: {e}")
            return {"error": str(e)}

    async def _market_intelligence_analysis(self, symbol: str) -> Dict[str, Any]:
        """Perform market intelligence analysis"""
        try:
            # Simulate market intelligence (in real implementation, would analyze market conditions)
            return {
                "market_regime": "bull_market",
                "sector_performance": "outperforming",
                "relative_strength": 1.15,
                "institutional_activity": "accumulating",
                "options_flow": "bullish",
                "earnings_outlook": "positive",
                "competitive_position": "strong",
                "intelligence_score": 8.2
            }

        except Exception as e:
            self.logger.error(f"❌ [INTELLIGENCE] Market intelligence failed for {symbol}: {e}")
            return {"error": str(e)}

    def _calculate_overall_score(self, analysis_results: List[Any]) -> float:
        """Calculate overall analysis score"""
        try:
            scores = []

            # Extract scores from each analysis
            for result in analysis_results:
                if isinstance(result, dict) and "error" not in result:
                    if "technical_score" in result:
                        scores.append(result["technical_score"])
                    if "sentiment_score" in result:
                        scores.append(result["sentiment_score"] * 10)  # Scale to 0-10
                    if "prediction_confidence" in result:
                        scores.append(result["prediction_confidence"] * 10)  # Scale to 0-10
                    if "intelligence_score" in result:
                        scores.append(result["intelligence_score"])

            # Calculate weighted average
            if scores:
                return sum(scores) / len(scores)
            else:
                return 5.0  # Neutral score

        except Exception as e:
            self.logger.error(f"❌ [SCORE] Failed to calculate overall score: {e}")
            return 5.0

    def _generate_recommendation(self, analysis_results: List[Any]) -> str:
        """Generate trading recommendation based on analysis"""
        try:
            overall_score = self._calculate_overall_score(analysis_results)

            if overall_score >= 7.5:
                return "STRONG_BUY"
            elif overall_score >= 6.5:
                return "BUY"
            elif overall_score >= 5.5:
                return "HOLD"
            elif overall_score >= 4.5:
                return "WEAK_HOLD"
            else:
                return "SELL"

        except Exception as e:
            self.logger.error(f"❌ [RECOMMENDATION] Failed to generate recommendation: {e}")
            return "HOLD"

    # ============================================================================
    # DATA VALIDATION AND QUALITY CONTROL
    # ============================================================================

    async def _validate_market_data(self, symbol: str, data: Dict[str, Any],
                                   source: str) -> Tuple[bool, DataQualityMetrics]:
        """Comprehensive market data validation"""
        try:
            issues = []
            quality_score = 100.0

            # 1. Basic data structure validation
            if not self._validate_data_structure(data):
                issues.append("Invalid data structure")
                quality_score -= 30

            # 2. Price validation
            price = data.get("price", 0)
            if not isinstance(price, (int, float)) or price <= 0:
                issues.append("Invalid price data")
                quality_score -= 25
            elif price < self.validation_rules["price_range"][0] or price > self.validation_rules["price_range"][1]:
                issues.append("Price outside valid range")
                quality_score -= 15

            # 3. Volume validation
            volume = data.get("volume", 0)
            if not isinstance(volume, (int, float)) or volume < 0:
                issues.append("Invalid volume data")
                quality_score -= 20
            elif volume > self.validation_rules["volume_range"][1]:
                issues.append("Volume suspiciously high")
                quality_score -= 10

            # 4. Source validation
            if source not in self.legitimate_data_sources:
                issues.append(f"Untrusted data source: {source}")
                quality_score -= 40

            # 5. Timestamp validation (if available)
            if "timestamp" in data:
                try:
                    timestamp = datetime.fromisoformat(str(data["timestamp"]).replace('Z', '+00:00'))
                    age = (datetime.now() - timestamp.replace(tzinfo=None)).total_seconds()
                    if age > self.validation_rules["max_data_age"]:
                        issues.append(f"Data too old: {age/60:.1f} minutes")
                        quality_score -= 15
                except:
                    issues.append("Invalid timestamp format")
                    quality_score -= 10

            # Determine if data is valid
            is_valid = quality_score >= 60.0  # Minimum 60% quality score

            # Create quality metrics
            quality_metrics = DataQualityMetrics(
                quality_score=quality_score,
                issues=issues,
                source=source,
                timestamp=datetime.now(),
                is_valid=is_valid
            )

            # Store quality metrics
            self.quality_metrics[f"{symbol}_{source}"] = quality_metrics

            if is_valid:
                self.data_failure_count = 0  # Reset failure count on success
                self.last_successful_data_time = datetime.now()
                if self.trading_halt_active:
                    self.trading_halt_active = False
                    self.logger.info("✅ [HALT] Trading halt lifted - data quality restored")

            return is_valid, quality_metrics

        except Exception as e:
            self.logger.error(f"❌ [VALIDATION] Data validation failed for {symbol}: {e}")
            return False, DataQualityMetrics(
                quality_score=0.0,
                issues=[str(e)],
                source=source,
                timestamp=datetime.now(),
                is_valid=False
            )

    def _validate_data_structure(self, data: Dict[str, Any]) -> bool:
        """Validate basic data structure"""
        try:
            # Check if data is a dictionary
            if not isinstance(data, dict):
                return False

            # Check for required fields
            required_fields = ["price"]  # Minimum required field
            for field in required_fields:
                if field not in data:
                    return False

            return True

        except Exception:
            return False

    # ============================================================================
    # CACHING AND PERFORMANCE OPTIMIZATION
    # ============================================================================

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        try:
            cache_dict = None

            if cache_key.startswith("quote_"):
                cache_dict = self.quote_cache
            elif cache_key.startswith("news_"):
                cache_dict = self.news_cache
            elif cache_key.startswith("comprehensive_"):
                cache_dict = self.analysis_cache

            if cache_dict and cache_key in cache_dict:
                cached_time = cache_dict[cache_key]["timestamp"]
                age = (datetime.now() - cached_time).total_seconds()
                return age < self.cache_ttl

            return False

        except Exception:
            return False

    def _cache_data(self, cache_key: str, data: Any, cache_type: str = "quote"):
        """Cache data with timestamp"""
        try:
            cache_entry = {
                "data": data,
                "timestamp": datetime.now()
            }

            if cache_type == "quote" or cache_key.startswith("quote_"):
                self.quote_cache[cache_key] = cache_entry
            elif cache_type == "news" or cache_key.startswith("news_"):
                self.news_cache[cache_key] = cache_entry
            elif cache_type == "analysis" or cache_key.startswith("comprehensive_"):
                self.analysis_cache[cache_key] = cache_entry

            # Periodic cache cleanup
            if (datetime.now() - self._last_cache_cleanup).total_seconds() > self._cache_cleanup_interval:
                self._cleanup_cache()

        except Exception as e:
            self.logger.error(f"❌ [CACHE] Failed to cache data: {e}")

    def _cleanup_cache(self):
        """Clean up expired cache entries"""
        try:
            current_time = datetime.now()

            # Clean quote cache
            expired_keys = [
                key for key, value in self.quote_cache.items()
                if (current_time - value["timestamp"]).total_seconds() > self.cache_ttl
            ]
            for key in expired_keys:
                del self.quote_cache[key]

            # Clean news cache
            expired_keys = [
                key for key, value in self.news_cache.items()
                if (current_time - value["timestamp"]).total_seconds() > self.cache_ttl
            ]
            for key in expired_keys:
                del self.news_cache[key]

            # Clean analysis cache
            expired_keys = [
                key for key, value in self.analysis_cache.items()
                if (current_time - value["timestamp"]).total_seconds() > self.cache_ttl
            ]
            for key in expired_keys:
                del self.analysis_cache[key]

            self._last_cache_cleanup = current_time
            self.logger.info(f"🧹 [CACHE] Cleaned up {len(expired_keys)} expired cache entries")

        except Exception as e:
            self.logger.error(f"❌ [CACHE] Cache cleanup failed: {e}")

    def _update_response_time(self, response_time: float):
        """Update average response time statistics"""
        try:
            current_avg = self.request_stats["avg_response_time"]
            total_requests = self.request_stats["successful_requests"]

            if total_requests == 1:
                self.request_stats["avg_response_time"] = response_time
            else:
                # Calculate running average
                self.request_stats["avg_response_time"] = (
                    (current_avg * (total_requests - 1)) + response_time
                ) / total_requests

        except Exception as e:
            self.logger.error(f"❌ [STATS] Failed to update response time: {e}")
