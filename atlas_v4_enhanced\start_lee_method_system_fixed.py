#!/usr/bin/env python3
"""
🚀 START LEE METHOD SCANNER - FULLY OPERATIONAL SYSTEM

This script starts the complete A.T.L.A.S. system with the fixed Lee Method Scanner
that now properly delivers desktop notifications and web interface signals.

Features:
- ✅ Desktop notifications for 75%+ confidence signals
- ✅ Real-time web interface at http://localhost:8002  
- ✅ API endpoints serving live signal data
- ✅ 250+ symbol scanning every 30 seconds
- ✅ Integrated signal storage and delivery
"""

import asyncio
import sys
import os
import webbrowser
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

async def start_system():
    """Start the complete Lee Method system"""
    
    print("🚀 STARTING A.T.L.A.S. LEE METHOD SCANNER")
    print("=" * 50)
    print("✅ Desktop notifications: ENABLED")
    print("✅ Web interface: http://localhost:8002")
    print("✅ API endpoints: ACTIVE")
    print("✅ Symbol scanning: 250+ symbols every 30s")
    print("=" * 50)
    
    try:
        # Import the server module
        print("📡 Starting A.T.L.A.S. server...")
        
        # Start the FastAPI server
        import uvicorn
        from atlas_server import app
        
        print("🌐 Server starting on http://localhost:8002")
        print("🔔 Desktop notifications will appear for high-confidence signals")
        print("📊 Lee Method signals will be available in the web interface")
        print()
        print("Press Ctrl+C to stop the system")
        print("=" * 50)
        
        # Open web browser after a short delay
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:8002')
                print("🌐 Web interface opened in browser")
            except:
                print("⚠️  Could not open browser automatically")
                print("   Please visit: http://localhost:8002")
        
        # Start browser opener in background
        import threading
        browser_thread = threading.Thread(target=open_browser, daemon=True)
        browser_thread.start()
        
        # Start the server
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8002,
            log_level="info",
            access_log=True
        )
        
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        print("\n🛑 System shutdown requested")
        print("✅ Lee Method Scanner stopped")
        
    except Exception as e:
        print(f"❌ System startup failed: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check that port 8002 is available")
        print("3. Verify FMP API key is configured in config.py")
        
        # Fallback: Try to start just the scanner without web interface
        print("\n🔄 Attempting fallback mode (scanner only)...")
        try:
            from atlas_orchestrator import AtlasOrchestrator
            
            orchestrator = AtlasOrchestrator()
            await orchestrator.initialize()
            
            print("✅ Lee Method Scanner started in fallback mode")
            print("🔔 Desktop notifications active")
            print("❌ Web interface unavailable")
            
            # Keep running
            while True:
                await asyncio.sleep(60)
                print(f"📊 Scanner running... {datetime.now().strftime('%H:%M:%S')}")
                
        except Exception as fallback_error:
            print(f"❌ Fallback mode also failed: {fallback_error}")
            print("Please check the system configuration and try again.")

def main():
    """Main entry point"""
    try:
        asyncio.run(start_system())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
