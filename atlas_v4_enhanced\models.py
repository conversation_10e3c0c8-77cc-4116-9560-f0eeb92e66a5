"""
A.T.L.A.S Models - Clean Implementation
This file provides all required data models for the Atlas trading system.
"""

import sys
import os
from datetime import datetime
from typing import Optional, Dict, List, Any
from dataclasses import dataclass
from enum import Enum

# Add consolidated path to imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'core'))

# Try to import consolidated models, but always define fallbacks
CONSOLIDATED_MODELS_AVAILABLE = False
try:
    from models import *
    CONSOLIDATED_MODELS_AVAILABLE = True
except ImportError:
    pass

# Define all required models
class EngineStatus(Enum):
    """Engine status enumeration"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    STOPPED = "stopped"
    FAILED = "failed"
    MAINTENANCE = "maintenance"

class SignalStrength(Enum):
    """Signal strength enumeration"""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"

class AlertType(Enum):
    """Alert type enumeration"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class AlertPriority(Enum):
    """Alert priority enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"

class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    BRACKET = "bracket"
    TRAILING_STOP = "trailing_stop"

class OrderStatus(Enum):
    """Order status enumeration"""
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    REJECTED = "rejected"

class CommunicationMode(Enum):
    """Communication mode enumeration"""
    CHAT = "chat"
    VOICE = "voice"
    EMAIL = "email"
    SMS = "sms"
    NOTIFICATION = "notification"

@dataclass
class Quote:
    """Stock quote data structure"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    open: Optional[float] = None
    previous_close: Optional[float] = None

@dataclass
class Order:
    """Trading order data structure"""
    symbol: str
    quantity: float
    side: OrderSide
    type: OrderType
    timestamp: datetime
    id: Optional[str] = None
    price: Optional[float] = None
    stop_price: Optional[float] = None
    status: str = "NEW"
    filled_quantity: float = 0.0

@dataclass
class Position:
    """Trading position data structure"""
    symbol: str
    quantity: float
    average_price: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime

@dataclass
class Trade:
    """Trade execution data structure"""
    symbol: str
    action: str
    quantity: float
    price: float
    timestamp: datetime
    trade_id: str
    status: str
    commission: float = 0.0

@dataclass
class TTMSqueezeSignal:
    """TTM Squeeze signal data structure"""
    symbol: str
    signal_type: str
    strength: SignalStrength
    confidence: float
    price: float
    timestamp: datetime
    criteria_met: List[str]
    technical_indicators: Dict[str, float]

@dataclass
class LeeMethodSignal:
    """Lee Method signal data structure"""
    symbol: str
    pattern_type: str
    confidence: float
    entry_price: float
    target_price: float
    stop_loss: float
    timestamp: datetime
    criteria_scores: Dict[str, float]

@dataclass
class TradingSignal:
    """General trading signal data structure"""
    symbol: str
    signal_type: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    price: float
    timestamp: datetime
    source: str
    metadata: Dict[str, Any]

@dataclass
class PortfolioSummary:
    """Portfolio summary data structure"""
    total_value: float
    cash: float
    buying_power: float
    day_pnl: float
    total_pnl: float
    positions_count: int
    timestamp: datetime

@dataclass
class MarketData:
    """Market data container"""
    symbol: str
    quotes: List[Any]
    historical_data: Optional[Dict[str, Any]] = None
    real_time_data: Optional[Dict[str, Any]] = None

@dataclass
class ScanResult:
    """Scanner result data structure"""
    symbol: str
    pattern_found: bool
    pattern_type: str
    confidence: float
    signal_strength: SignalStrength
    price: float
    volume: int
    timestamp: datetime

@dataclass
class AlertSignal:
    """Alert signal data structure"""
    alert_id: str
    symbol: str
    alert_type: AlertType
    priority: AlertPriority
    message: str
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class RiskMetrics:
    """Risk metrics data structure"""
    symbol: str
    var_95: float  # Value at Risk 95%
    volatility: float
    beta: float
    sharpe_ratio: float
    max_drawdown: float
    timestamp: datetime

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    timestamp: datetime

@dataclass
class MLPredictionResult:
    """Machine learning prediction result"""
    symbol: str
    prediction_type: str
    predicted_value: float
    confidence: float
    model_used: str
    features_used: List[str]
    timestamp: datetime

@dataclass
class NewsItem:
    """News item data structure"""
    title: str
    content: str
    source: str
    url: str
    timestamp: datetime
    sentiment_score: Optional[float] = None
    relevance_score: Optional[float] = None

@dataclass
class SentimentData:
    """Sentiment analysis data structure"""
    symbol: str
    sentiment_score: float
    sentiment_label: str  # POSITIVE, NEGATIVE, NEUTRAL
    confidence: float
    source: str
    timestamp: datetime

@dataclass
class TradingPlanTarget:
    """Trading plan target data structure"""
    price: float
    probability: float
    timeframe: str
    reasoning: str

@dataclass
class TradingOpportunity:
    """Trading opportunity data structure"""
    symbol: str
    opportunity_type: str
    confidence: float
    entry_price: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: float
    timestamp: datetime
    reasoning: str

@dataclass
class OptionsChain:
    """Options chain data structure"""
    symbol: str
    expiration_date: datetime
    strike_price: float
    call_price: float
    put_price: float
    call_volume: int
    put_volume: int
    timestamp: datetime

@dataclass
class MarketSentiment:
    """Market sentiment data structure"""
    symbol: str
    sentiment_score: float
    sentiment_label: str
    confidence: float
    sources: List[str]
    timestamp: datetime

@dataclass
class TradingPlanAlert:
    """Trading plan alert data structure"""
    alert_id: str
    plan_id: str
    symbol: str
    alert_type: str
    message: str
    priority: AlertPriority
    timestamp: datetime
    triggered_price: float
    action_required: str

@dataclass
class TradingPlanExecution:
    """Trading plan execution data structure"""
    execution_id: str
    plan_id: str
    symbol: str
    action: str
    quantity: float
    price: float
    timestamp: datetime
    status: str
    commission: float = 0.0
    notes: Optional[str] = None

@dataclass
class AIResponse:
    """AI response data structure"""
    response_id: str
    query: str
    response: str
    confidence: float
    model_used: str
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class EmotionalState:
    """Emotional state data structure"""
    state_id: str
    emotion: str
    intensity: float
    confidence: float
    timestamp: datetime
    context: str

@dataclass
class TechnicalIndicators:
    """Technical indicators data structure"""
    symbol: str
    rsi: Optional[float] = None
    macd: Optional[float] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    volume_avg: Optional[float] = None
    timestamp: datetime = None

@dataclass
class RiskAssessment:
    """Risk assessment data structure"""
    assessment_id: str
    symbol: str
    risk_level: str
    risk_score: float
    factors: List[str]
    recommendations: List[str]
    timestamp: datetime

@dataclass
class TradingGoal:
    """Trading goal data structure"""
    goal_id: str
    description: str
    target_value: float
    current_value: float
    deadline: datetime
    priority: str
    status: str
    created_at: datetime

@dataclass
class ComprehensiveTradingPlan:
    """Comprehensive trading plan data structure"""
    symbol: str
    plan_id: str
    strategy: str
    entry_price: float
    target_price: float
    stop_loss: float
    position_size: float
    risk_amount: float
    confidence: float
    timeframe: str
    created_at: datetime
    status: str = "PENDING"
    notes: Optional[str] = None
    targets: List[TradingPlanTarget] = None

class SystemHealth:
    """System health monitoring"""
    def __init__(self):
        self.status = EngineStatus.ACTIVE
        self.components = {}
        self.last_check = datetime.now()
        
    def update_component(self, component: str, status: EngineStatus):
        self.components[component] = {
            "status": status,
            "last_update": datetime.now()
        }
        
    def get_overall_status(self) -> EngineStatus:
        if not self.components:
            return EngineStatus.INITIALIZING
            
        statuses = [comp["status"] for comp in self.components.values()]
        
        if any(status == EngineStatus.FAILED for status in statuses):
            return EngineStatus.FAILED
        elif any(status == EngineStatus.MAINTENANCE for status in statuses):
            return EngineStatus.MAINTENANCE
        elif all(status == EngineStatus.ACTIVE for status in statuses):
            return EngineStatus.ACTIVE
        else:
            return EngineStatus.INITIALIZING

# Export all models for compatibility
__all__ = [
    'EngineStatus', 'SignalStrength', 'AlertType', 'AlertPriority',
    'OrderSide', 'OrderType', 'OrderStatus', 'CommunicationMode',
    'Quote', 'Order', 'Position', 'Trade',
    'TTMSqueezeSignal', 'LeeMethodSignal', 'TradingSignal',
    'PortfolioSummary', 'MarketData', 'ScanResult',
    'AlertSignal', 'RiskMetrics', 'PerformanceMetrics', 'MLPredictionResult',
    'NewsItem', 'SentimentData', 'SystemHealth', 'ComprehensiveTradingPlan',
    'TradingPlanTarget', 'TradingOpportunity', 'OptionsChain', 'MarketSentiment',
    'TradingPlanAlert', 'TradingPlanExecution', 'AIResponse', 'EmotionalState',
    'TechnicalIndicators', 'RiskAssessment', 'TradingGoal'
]
