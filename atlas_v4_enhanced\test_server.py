#!/usr/bin/env python3
"""
Minimal test server to verify basic functionality
"""

import uvicorn
from fastapi import Fast<PERSON><PERSON>
from fastapi.responses import HTMLResponse, JSONResponse

print("🧪 Creating minimal test server...")

app = FastAPI(title="A.T.L.A.S. Test Server")

@app.get("/")
async def root():
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>A.T.L.A.S. Test Server</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }
            .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .success { color: #28a745; font-weight: bold; }
            .info { color: #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 A.T.L.A.S. Test Server</h1>
            <p class="success">✅ Server is running successfully!</p>
            <p class="info">📡 Port 8002 is working</p>
            <p class="info">🌐 Web interface is functional</p>
            <p>This confirms the basic server setup is working. Now we can proceed with the full A.T.L.A.S. system.</p>
            
            <h2>🔧 Next Steps:</h2>
            <ul>
                <li>✅ Basic server: Working</li>
                <li>⏳ Full A.T.L.A.S. system: Ready to start</li>
                <li>⏳ Lee Method Scanner: Ready to initialize</li>
                <li>⏳ Desktop notifications: Ready to enable</li>
            </ul>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html)

@app.get("/test")
async def test():
    return JSONResponse(content={
        "status": "success",
        "message": "Test server is working",
        "port": 8002,
        "ready_for_atlas": True
    })

@app.get("/health")
async def health():
    return JSONResponse(content={"status": "healthy", "service": "test-server"})

if __name__ == "__main__":
    print("🚀 Starting test server on http://localhost:8002")
    print("✅ This will verify the basic setup works")
    uvicorn.run(app, host="0.0.0.0", port=8002, log_level="info")
