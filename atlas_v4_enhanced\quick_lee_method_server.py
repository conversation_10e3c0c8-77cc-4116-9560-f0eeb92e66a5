#!/usr/bin/env python3
"""
Quick Lee Method Server - Lightweight version for testing
Focuses only on Lee Method Scanner functionality
"""

import asyncio
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import logging
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="A.T.L.A.S. Lee Method Scanner",
    description="Quick Lee Method Scanner Interface",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global orchestrator instance
orchestrator = None

@app.on_event("startup")
async def startup_event():
    """Initialize the Lee Method Scanner on startup"""
    global orchestrator
    try:
        logger.info("🚀 Starting Quick Lee Method Scanner...")
        
        # Initialize only the Lee Method components
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        
        # Initialize only essential engines
        await orchestrator.initialize_engine('database')
        await orchestrator.initialize_engine('market')
        await orchestrator.initialize_engine('lee_method')
        
        logger.info("✅ Quick Lee Method Scanner ready!")
        logger.info("🔔 Desktop notifications enabled")
        logger.info("📊 Web interface available at http://localhost:8002")
        
    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")
        import traceback
        traceback.print_exc()

@app.get("/")
async def root():
    """Serve the main web interface"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>A.T.L.A.S. Lee Method Scanner</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
            .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
            .signals { margin-top: 20px; }
            .signal-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }
            .signal-header { font-weight: bold; font-size: 18px; color: #007bff; }
            .signal-details { margin-top: 10px; }
            .confidence { font-weight: bold; }
            .high-confidence { color: #28a745; }
            .medium-confidence { color: #ffc107; }
            .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 0; }
            .refresh-btn:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🚀 A.T.L.A.S. Lee Method Scanner</h1>
                <p>Real-time detection of 3+ consecutive declining bar patterns</p>
            </div>
            
            <div class="status success">
                ✅ <strong>System Status:</strong> Lee Method Scanner is operational
            </div>
            
            <div class="status info">
                🔔 <strong>Desktop Notifications:</strong> Enabled for 75%+ confidence signals
            </div>
            
            <button class="refresh-btn" onclick="loadSignals()">🔄 Refresh Signals</button>
            
            <div class="signals">
                <h2>📊 Active Lee Method Signals</h2>
                <div id="signals-container">
                    <p>Loading signals...</p>
                </div>
            </div>
        </div>
        
        <script>
            async function loadSignals() {
                try {
                    const response = await fetch('/api/v1/lee_method/signals/active');
                    const data = await response.json();
                    
                    const container = document.getElementById('signals-container');
                    
                    if (data.success && data.signals && data.signals.length > 0) {
                        container.innerHTML = data.signals.map(signal => `
                            <div class="signal-card">
                                <div class="signal-header">${signal.symbol}</div>
                                <div class="signal-details">
                                    <div class="confidence ${signal.confidence >= 0.75 ? 'high-confidence' : 'medium-confidence'}">
                                        Confidence: ${(signal.confidence * 100).toFixed(1)}%
                                    </div>
                                    <div>Price: $${signal.price ? signal.price.toFixed(2) : 'N/A'}</div>
                                    <div>Description: ${signal.description || 'No description'}</div>
                                    <div>Detected: ${new Date(signal.timestamp).toLocaleString()}</div>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        container.innerHTML = '<p>No active signals found. The scanner is monitoring 250+ symbols for 3+ consecutive declining bar patterns.</p>';
                    }
                } catch (error) {
                    document.getElementById('signals-container').innerHTML = `<p>Error loading signals: ${error.message}</p>`;
                }
            }
            
            // Load signals on page load
            loadSignals();
            
            // Auto-refresh every 30 seconds
            setInterval(loadSignals, 30000);
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/api/v1/lee_method/signals/active")
async def get_active_lee_method_signals():
    """Get high-confidence Lee Method signals"""
    try:
        if not orchestrator:
            return JSONResponse(content={
                "success": False,
                "error": "Scanner not initialized",
                "signals": []
            })
        
        # Get signals from orchestrator
        signals_data = await orchestrator.get_lee_method_signals()
        all_signals = signals_data.get('signals', [])
        
        # Filter for high-confidence signals
        active_signals = []
        for signal in all_signals:
            confidence = signal.get('confidence', 0)
            description = signal.get('description', '')
            
            if confidence >= 0.75 or '3 consecutive' in description or 'Active decline' in description:
                active_signals.append(signal)
        
        # Sort by confidence
        active_signals.sort(key=lambda x: x.get('confidence', 0), reverse=True)
        
        logger.info(f"[API] Serving {len(active_signals)} high-confidence signals")
        
        return JSONResponse(content={
            "success": True,
            "signals": active_signals[:10],
            "total_active": len(active_signals),
            "response_time": "real-time"
        })
        
    except Exception as e:
        logger.error(f"[API] Error getting signals: {e}")
        return JSONResponse(content={
            "success": False,
            "error": str(e),
            "signals": []
        })

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return JSONResponse(content={
        "status": "healthy",
        "service": "A.T.L.A.S. Lee Method Scanner",
        "scanner_ready": orchestrator is not None
    })

if __name__ == "__main__":
    print("🚀 Starting Quick Lee Method Scanner...")
    print("✅ Lightweight version for fast startup")
    print("🌐 Web interface: http://localhost:8002")
    print("🔔 Desktop notifications: Enabled")
    print("=" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8002, log_level="info")
