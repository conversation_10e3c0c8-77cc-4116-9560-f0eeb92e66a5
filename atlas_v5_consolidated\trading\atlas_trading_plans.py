"""
A.T.L.A.S. Consolidated Trading Plans Engine
Combines all trading plan generation functionality:
- Comprehensive trading plan generation with specific dollar targets
- Goal-based strategy generation and customization
- Lee Method integration for opportunity scanning
- Risk assessment and portfolio integration
- Plan monitoring and execution tracking
- Performance analytics and optimization
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json

# Core imports
from models import (
    ComprehensiveTradingPlan, TradingPlanTarget, TradingOpportunity,
    PortfolioIntegration, TradingPlanScenario, TradingPlanMonitoring,
    TradingPlanAlert, TradingPlanExecution, AlertPriority, EngineStatus
)

logger = logging.getLogger(__name__)

class AtlasTradingPlansEngine:
    """
    Consolidated Trading Plans Engine
    Combines comprehensive plan generation, goal-based strategies, and monitoring
    """
    
    def __init__(self):
        self.status = EngineStatus.INITIALIZING
        self.logger = logging.getLogger(__name__)
        
        # Plan storage and tracking
        self.active_plans: Dict[str, ComprehensiveTradingPlan] = {}
        self.plan_history: List[ComprehensiveTradingPlan] = []
        self.plan_templates: Dict[str, Dict[str, Any]] = {}
        
        # Goal-based strategy templates
        self.goal_templates: Dict[str, Dict[str, Any]] = {}
        
        # Performance tracking
        self.plans_generated = 0
        self.plan_success_rate = 0.0
        self.execution_stats = {
            "total_plans": 0,
            "successful_plans": 0,
            "failed_plans": 0,
            "avg_return": 0.0
        }
        
        # Configuration
        self.default_risk_per_trade = 0.02  # 2% risk per trade
        self.max_portfolio_risk = 0.10  # 10% max portfolio risk
        self.min_confidence_threshold = 70.0  # Minimum confidence for recommendations
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all trading plan components"""
        try:
            # Initialize plan templates
            self._initialize_plan_templates()
            
            # Initialize goal-based strategy templates
            self._initialize_goal_templates()
            
            # Initialize monitoring systems
            self._initialize_monitoring()
            
            self.status = EngineStatus.RUNNING
            self.logger.info("✅ [TRADING_PLANS] All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ [TRADING_PLANS] Initialization failed: {e}")
            self.status = EngineStatus.ERROR
            raise
    
    def _initialize_plan_templates(self):
        """Initialize trading plan templates"""
        self.plan_templates = {
            "conservative": {
                "risk_per_trade": 0.01,  # 1% risk per trade
                "max_positions": 3,
                "target_return": 0.08,  # 8% annual return
                "strategies": ["value_investing", "dividend_growth", "defensive_stocks"]
            },
            "moderate": {
                "risk_per_trade": 0.02,  # 2% risk per trade
                "max_positions": 5,
                "target_return": 0.15,  # 15% annual return
                "strategies": ["balanced_growth", "momentum", "lee_method"]
            },
            "aggressive": {
                "risk_per_trade": 0.03,  # 3% risk per trade
                "max_positions": 8,
                "target_return": 0.25,  # 25% annual return
                "strategies": ["growth_momentum", "options_strategies", "high_beta"]
            }
        }
        self.logger.info("📋 [TEMPLATES] Plan templates initialized")
    
    def _initialize_goal_templates(self):
        """Initialize goal-based strategy templates"""
        self.goal_templates = {
            "daily_income": {
                "description": "Generate consistent daily income through trading",
                "strategies": ["day_trading", "scalping", "momentum_trading"],
                "risk_level": "moderate",
                "expected_return": 0.20,
                "time_commitment": "high",
                "capital_requirements": 25000  # PDT rule
            },
            "weekly_profits": {
                "description": "Generate weekly profits through swing trading",
                "strategies": ["swing_trading", "lee_method", "technical_analysis"],
                "risk_level": "moderate",
                "expected_return": 0.18,
                "time_commitment": "moderate",
                "capital_requirements": 10000
            },
            "monthly_growth": {
                "description": "Achieve monthly growth through position trading",
                "strategies": ["position_trading", "trend_following", "fundamental_analysis"],
                "risk_level": "low",
                "expected_return": 0.12,
                "time_commitment": "low",
                "capital_requirements": 5000
            },
            "wealth_building": {
                "description": "Long-term wealth building through diversified strategies",
                "strategies": ["buy_and_hold", "dividend_growth", "index_investing"],
                "risk_level": "low",
                "expected_return": 0.10,
                "time_commitment": "low",
                "capital_requirements": 1000
            },
            "aggressive_growth": {
                "description": "Maximize returns with higher risk tolerance",
                "strategies": ["momentum_trading", "options_strategies", "small_cap_growth"],
                "risk_level": "high",
                "expected_return": 0.30,
                "time_commitment": "high",
                "capital_requirements": 50000
            }
        }
        self.logger.info("🎯 [GOALS] Goal templates initialized")
    
    def _initialize_monitoring(self):
        """Initialize plan monitoring systems"""
        self.monitoring_metrics = {
            "plan_performance": {},
            "risk_metrics": {},
            "execution_quality": {},
            "market_conditions": {}
        }
        self.alert_conditions = []
        self.logger.info("📊 [MONITORING] Monitoring systems initialized")

    # ============================================================================
    # COMPREHENSIVE TRADING PLAN GENERATION
    # ============================================================================
    
    async def generate_comprehensive_trading_plan(
        self,
        target_profit: float,
        timeframe_days: int,
        starting_capital: float,
        risk_tolerance: str = "moderate"
    ) -> ComprehensiveTradingPlan:
        """
        Generate a comprehensive trading plan for specific profit target and timeframe
        
        Args:
            target_profit: Target profit amount in dollars
            timeframe_days: Target timeframe in days
            starting_capital: Starting capital amount
            risk_tolerance: Risk tolerance level (conservative, moderate, aggressive)
        
        Returns:
            ComprehensiveTradingPlan: Complete actionable trading plan
        """
        try:
            self.logger.info(f"📋 [PLAN_GEN] Generating plan: ${target_profit:,.2f} profit in {timeframe_days} days")
            
            # Generate unique plan ID
            plan_id = f"ATLAS_PLAN_{uuid.uuid4().hex[:8].upper()}"
            
            # Create financial target
            target = await self._create_financial_target(
                target_profit, timeframe_days, starting_capital, risk_tolerance
            )
            
            # Scan for trading opportunities
            opportunities = await self._scan_trading_opportunities(target)
            
            # Analyze portfolio integration
            portfolio_integration = await self._analyze_portfolio_integration(opportunities, target)
            
            # Generate alternative scenarios
            scenarios = await self._generate_alternative_scenarios(target, opportunities)
            
            # Create monitoring framework
            monitoring = await self._create_monitoring_framework(opportunities, target)
            
            # Calculate plan metrics
            total_expected_return = sum(getattr(opp, 'expected_profit', 0) for opp in opportunities)
            total_risk_amount = sum(getattr(opp, 'max_loss', 0) for opp in opportunities)
            plan_confidence = await self._calculate_plan_confidence(opportunities)
            
            # Create comprehensive trading plan
            trading_plan = ComprehensiveTradingPlan(
                plan_id=plan_id,
                plan_name=f"${target_profit:,.0f} Profit Plan - {timeframe_days} Days",
                target=target,
                opportunities=opportunities,
                portfolio_integration=portfolio_integration,
                scenarios=scenarios,
                monitoring=monitoring,
                created_at=datetime.now(),
                status="active"
            )
            
            # Store active plan
            self.active_plans[plan_id] = trading_plan
            self.plan_history.append(trading_plan)
            self.plans_generated += 1
            self.execution_stats["total_plans"] += 1
            
            self.logger.info(f"✅ [PLAN_GEN] Generated plan {plan_id} with {len(opportunities)} opportunities")
            return trading_plan
            
        except Exception as e:
            self.logger.error(f"❌ [PLAN_GEN] Plan generation failed: {e}")
            self.execution_stats["failed_plans"] += 1
            raise
    
    async def _create_financial_target(
        self, target_profit: float, timeframe_days: int, 
        starting_capital: float, risk_tolerance: str
    ) -> TradingPlanTarget:
        """Create financial target structure"""
        try:
            # Calculate required return rate
            required_return_rate = target_profit / starting_capital
            annualized_return = (required_return_rate * 365) / timeframe_days
            
            # Get risk parameters from template
            template = self.plan_templates.get(risk_tolerance, self.plan_templates["moderate"])
            
            return TradingPlanTarget(
                profit_target=target_profit,
                timeframe_days=timeframe_days,
                starting_capital=starting_capital,
                risk_tolerance=risk_tolerance,
                required_return_rate=required_return_rate,
                annualized_return_target=annualized_return,
                max_drawdown_limit=starting_capital * 0.10,  # 10% max drawdown
                risk_per_trade=template["risk_per_trade"],
                max_positions=template["max_positions"]
            )
            
        except Exception as e:
            self.logger.error(f"❌ [TARGET] Failed to create financial target: {e}")
            raise
    
    async def _scan_trading_opportunities(self, target: TradingPlanTarget) -> List[TradingOpportunity]:
        """Scan for trading opportunities based on target parameters"""
        try:
            opportunities = []
            
            # Sample symbols for opportunities (in real implementation, this would use Lee Method scanner)
            symbols = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "META", "NFLX", "CRM", "ADBE"]
            
            # Calculate number of opportunities needed
            num_opportunities = min(target.max_positions, max(2, int(target.profit_target / 500)))
            
            for i in range(num_opportunities):
                symbol = symbols[i % len(symbols)]
                
                # Generate opportunity (simulated - in real implementation would use market data)
                opportunity = await self._generate_trading_opportunity(symbol, target)
                if opportunity:
                    opportunities.append(opportunity)
            
            self.logger.info(f"🔍 [SCAN] Found {len(opportunities)} trading opportunities")
            return opportunities
            
        except Exception as e:
            self.logger.error(f"❌ [SCAN] Failed to scan opportunities: {e}")
            return []
    
    async def _generate_trading_opportunity(self, symbol: str, target: TradingPlanTarget) -> Optional[TradingOpportunity]:
        """Generate a trading opportunity for a specific symbol"""
        try:
            # Simulate market data (in real implementation, would fetch actual data)
            base_prices = {
                "AAPL": 150.0, "MSFT": 300.0, "GOOGL": 2500.0, "AMZN": 3000.0,
                "TSLA": 200.0, "NVDA": 400.0, "META": 250.0, "NFLX": 400.0,
                "CRM": 200.0, "ADBE": 500.0
            }
            
            current_price = base_prices.get(symbol, 100.0)
            
            # Calculate position sizing based on risk parameters
            risk_amount = target.starting_capital * target.risk_per_trade
            position_size = risk_amount / (current_price * 0.05)  # 5% stop loss
            
            # Calculate targets
            entry_price = current_price
            target_price = current_price * 1.15  # 15% target
            stop_loss = current_price * 0.95  # 5% stop loss
            
            # Calculate profit/loss amounts
            max_profit = position_size * (target_price - entry_price)
            max_loss = position_size * (entry_price - stop_loss)
            
            return TradingOpportunity(
                symbol=symbol,
                action="BUY",
                entry_price=entry_price,
                target_price=target_price,
                stop_loss=stop_loss,
                position_size=position_size,
                confidence_score=0.75 + (i * 0.05),  # Varying confidence
                reasoning=f"Technical analysis indicates strong momentum for {symbol}",
                timeframe=f"{target.timeframe_days} days",
                risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 3.0,
                expected_return=0.15,  # 15% expected return
                max_profit_dollars=max_profit,
                max_loss_dollars=max_loss
            )
            
        except Exception as e:
            self.logger.error(f"❌ [OPPORTUNITY] Failed to generate opportunity for {symbol}: {e}")
            return None

    async def _analyze_portfolio_integration(self, opportunities: List[TradingOpportunity],
                                           target: TradingPlanTarget) -> PortfolioIntegration:
        """Analyze how opportunities integrate with portfolio"""
        try:
            # Calculate total capital allocation
            total_allocation = sum(opp.position_size * opp.entry_price for opp in opportunities)
            cash_allocation = target.starting_capital - total_allocation

            # Calculate risk budget
            total_risk = sum(getattr(opp, 'max_loss_dollars', 0) for opp in opportunities)
            risk_budget = target.starting_capital * target.risk_per_trade * len(opportunities)

            return PortfolioIntegration(
                current_positions=[],  # Would be populated with actual positions
                cash_allocation=cash_allocation,
                risk_budget=risk_budget,
                total_allocation=total_allocation,
                diversification_score=min(1.0, len(opportunities) / 5.0),  # Max score with 5+ positions
                correlation_analysis={"low_correlation": True, "sector_diversification": True}
            )

        except Exception as e:
            self.logger.error(f"❌ [PORTFOLIO] Failed to analyze portfolio integration: {e}")
            return PortfolioIntegration(current_positions=[], cash_allocation=0, risk_budget=0)

    async def _generate_alternative_scenarios(self, target: TradingPlanTarget,
                                            opportunities: List[TradingOpportunity]) -> List[TradingPlanScenario]:
        """Generate alternative scenarios for the trading plan"""
        try:
            scenarios = []

            # Best case scenario
            best_case_return = sum(getattr(opp, 'max_profit_dollars', 0) for opp in opportunities)
            scenarios.append(TradingPlanScenario(
                name="Best Case",
                description="All trades hit targets",
                probability=0.15,
                expected_return=best_case_return,
                risk_level="low",
                conditions=["Strong market conditions", "All signals confirm", "No major news events"]
            ))

            # Most likely scenario
            likely_return = sum(getattr(opp, 'max_profit_dollars', 0) * 0.6 for opp in opportunities)
            scenarios.append(TradingPlanScenario(
                name="Most Likely",
                description="60% of trades successful",
                probability=0.60,
                expected_return=likely_return,
                risk_level="moderate",
                conditions=["Normal market conditions", "Mixed signals", "Typical volatility"]
            ))

            # Worst case scenario
            worst_case_loss = sum(getattr(opp, 'max_loss_dollars', 0) for opp in opportunities)
            scenarios.append(TradingPlanScenario(
                name="Worst Case",
                description="All trades hit stop losses",
                probability=0.10,
                expected_return=-worst_case_loss,
                risk_level="high",
                conditions=["Market crash", "All signals fail", "Major negative events"]
            ))

            return scenarios

        except Exception as e:
            self.logger.error(f"❌ [SCENARIOS] Failed to generate scenarios: {e}")
            return []

    async def _create_monitoring_framework(self, opportunities: List[TradingOpportunity],
                                         target: TradingPlanTarget) -> TradingPlanMonitoring:
        """Create monitoring framework for the trading plan"""
        try:
            # Define key metrics to monitor
            key_metrics = [
                "total_return", "win_rate", "average_return_per_trade",
                "maximum_drawdown", "sharpe_ratio", "risk_adjusted_return"
            ]

            # Define alert conditions
            alert_conditions = [
                f"Daily loss exceeds ${target.starting_capital * 0.05:.2f}",
                f"Total drawdown exceeds {target.max_drawdown_limit:.2f}",
                "Win rate falls below 50%",
                "Any position loses more than 10%"
            ]

            return TradingPlanMonitoring(
                key_metrics=key_metrics,
                alert_conditions=alert_conditions,
                review_schedule="daily",
                performance_benchmarks={
                    "target_return": target.profit_target,
                    "max_drawdown": target.max_drawdown_limit,
                    "min_win_rate": 0.60
                }
            )

        except Exception as e:
            self.logger.error(f"❌ [MONITORING] Failed to create monitoring framework: {e}")
            return TradingPlanMonitoring(key_metrics=[], alert_conditions=[], review_schedule="daily")

    async def _calculate_plan_confidence(self, opportunities: List[TradingOpportunity]) -> float:
        """Calculate overall confidence score for the trading plan"""
        try:
            if not opportunities:
                return 0.0

            # Calculate weighted average confidence
            total_weight = sum(getattr(opp, 'position_size', 1) * getattr(opp, 'entry_price', 100) for opp in opportunities)
            weighted_confidence = 0.0

            for opp in opportunities:
                weight = (getattr(opp, 'position_size', 1) * getattr(opp, 'entry_price', 100)) / total_weight
                weighted_confidence += getattr(opp, 'confidence_score', 0.5) * weight

            # Adjust for diversification (more opportunities = higher confidence)
            diversification_bonus = min(0.1, len(opportunities) * 0.02)

            final_confidence = min(1.0, weighted_confidence + diversification_bonus)

            self.logger.info(f"📊 [CONFIDENCE] Plan confidence calculated: {final_confidence:.2%}")
            return final_confidence

        except Exception as e:
            self.logger.error(f"❌ [CONFIDENCE] Failed to calculate plan confidence: {e}")
            return 0.5  # Default moderate confidence

    # ============================================================================
    # GOAL-BASED STRATEGY GENERATION
    # ============================================================================

    async def generate_strategy_for_goal(self, goal: str, user_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate strategy based on user goal"""
        try:
            if goal not in self.goal_templates:
                return {'error': f'Unknown goal: {goal}. Available goals: {list(self.goal_templates.keys())}'}

            template = self.goal_templates[goal]

            # Extract user profile information
            capital = user_profile.get('capital', 10000)
            experience = user_profile.get('experience', 'beginner')
            time_available = user_profile.get('time_available', 'low')
            risk_tolerance = user_profile.get('risk_tolerance', 'moderate')

            # Adjust strategy based on user profile
            adjusted_strategies = self._adjust_strategies_for_user(template['strategies'], user_profile)
            adjusted_return = self._adjust_expected_return(template['expected_return'], experience, capital)

            # Generate implementation steps
            implementation_steps = await self._generate_implementation_steps(goal, user_profile)

            # Calculate capital allocation
            allocation = self._calculate_allocation(capital, template)

            return {
                'goal': goal,
                'description': template['description'],
                'recommended_strategies': adjusted_strategies,
                'risk_level': template['risk_level'],
                'expected_annual_return': adjusted_return,
                'time_commitment': template['time_commitment'],
                'capital_requirements': template['capital_requirements'],
                'capital_allocation': allocation,
                'implementation_steps': implementation_steps,
                'success_probability': self._calculate_success_probability(goal, user_profile),
                'timeline': self._generate_timeline(goal, user_profile)
            }

        except Exception as e:
            self.logger.error(f"❌ [GOAL_STRATEGY] Goal-based strategy generation failed: {e}")
            return {'error': str(e)}

    def _adjust_strategies_for_user(self, base_strategies: List[str], user_profile: Dict[str, Any]) -> List[str]:
        """Adjust strategies based on user profile"""
        try:
            experience = user_profile.get('experience', 'beginner')
            time_available = user_profile.get('time_available', 'low')

            adjusted = base_strategies.copy()

            # Adjust for experience level
            if experience == 'beginner':
                # Remove complex strategies for beginners
                complex_strategies = ['options_strategies', 'scalping', 'day_trading']
                adjusted = [s for s in adjusted if s not in complex_strategies]
                # Add beginner-friendly strategies
                if 'buy_and_hold' not in adjusted:
                    adjusted.append('buy_and_hold')

            # Adjust for time availability
            if time_available == 'low':
                # Remove time-intensive strategies
                time_intensive = ['day_trading', 'scalping', 'momentum_trading']
                adjusted = [s for s in adjusted if s not in time_intensive]
                # Add passive strategies
                if 'index_investing' not in adjusted:
                    adjusted.append('index_investing')

            return adjusted

        except Exception as e:
            self.logger.error(f"❌ [ADJUST] Failed to adjust strategies: {e}")
            return base_strategies

    def _adjust_expected_return(self, base_return: float, experience: str, capital: float) -> float:
        """Adjust expected return based on user factors"""
        try:
            adjusted_return = base_return

            # Adjust for experience
            if experience == 'beginner':
                adjusted_return *= 0.7  # Reduce expectations for beginners
            elif experience == 'expert':
                adjusted_return *= 1.2  # Increase for experts

            # Adjust for capital (larger capital may have lower returns due to liquidity)
            if capital > 100000:
                adjusted_return *= 0.9
            elif capital < 5000:
                adjusted_return *= 0.8  # Smaller accounts face more challenges

            return max(0.05, min(0.50, adjusted_return))  # Cap between 5% and 50%

        except Exception as e:
            self.logger.error(f"❌ [RETURN_ADJUST] Failed to adjust expected return: {e}")
            return base_return

    async def _generate_implementation_steps(self, goal: str, user_profile: Dict[str, Any]) -> List[str]:
        """Generate step-by-step implementation guide"""
        try:
            steps = []

            # Common initial steps
            steps.extend([
                "1. Complete risk assessment and set position sizing rules",
                "2. Set up paper trading account to practice strategies",
                "3. Define clear entry and exit criteria for each strategy"
            ])

            # Goal-specific steps
            if goal == "daily_income":
                steps.extend([
                    "4. Ensure account meets PDT requirements ($25,000 minimum)",
                    "5. Set up real-time market data and scanning tools",
                    "6. Practice day trading strategies in paper trading",
                    "7. Start with small position sizes and gradually increase"
                ])
            elif goal == "weekly_profits":
                steps.extend([
                    "4. Set up swing trading watchlists and alerts",
                    "5. Learn technical analysis and chart patterns",
                    "6. Practice Lee Method pattern recognition",
                    "7. Implement systematic entry and exit rules"
                ])
            elif goal == "monthly_growth":
                steps.extend([
                    "4. Research and select quality growth stocks",
                    "5. Set up monthly review and rebalancing schedule",
                    "6. Implement trend-following indicators",
                    "7. Monitor fundamental changes in holdings"
                ])

            # Final steps
            steps.extend([
                f"{len(steps)+1}. Start live trading with minimal position sizes",
                f"{len(steps)+2}. Track performance and adjust strategies as needed",
                f"{len(steps)+3}. Gradually increase position sizes as confidence grows"
            ])

            return steps

        except Exception as e:
            self.logger.error(f"❌ [IMPLEMENTATION] Failed to generate implementation steps: {e}")
            return ["Error generating implementation steps"]

    def _calculate_allocation(self, capital: float, template: Dict[str, Any]) -> Dict[str, float]:
        """Calculate capital allocation based on template and capital"""
        try:
            # Base allocation percentages
            base_allocation = {
                "trading_capital": 0.80,  # 80% for active trading
                "cash_reserve": 0.15,     # 15% cash reserve
                "emergency_fund": 0.05    # 5% emergency fund
            }

            # Adjust based on risk level
            risk_level = template.get('risk_level', 'moderate')
            if risk_level == 'low':
                base_allocation["trading_capital"] = 0.60
                base_allocation["cash_reserve"] = 0.30
                base_allocation["emergency_fund"] = 0.10
            elif risk_level == 'high':
                base_allocation["trading_capital"] = 0.90
                base_allocation["cash_reserve"] = 0.08
                base_allocation["emergency_fund"] = 0.02

            # Calculate dollar amounts
            allocation = {}
            for category, percentage in base_allocation.items():
                allocation[category] = capital * percentage

            return allocation

        except Exception as e:
            self.logger.error(f"❌ [ALLOCATION] Failed to calculate allocation: {e}")
            return {"trading_capital": capital * 0.8, "cash_reserve": capital * 0.2}

    def _calculate_success_probability(self, goal: str, user_profile: Dict[str, Any]) -> float:
        """Calculate probability of success for the goal"""
        try:
            base_probability = 0.60  # 60% base success rate

            # Adjust for experience
            experience = user_profile.get('experience', 'beginner')
            if experience == 'expert':
                base_probability += 0.20
            elif experience == 'intermediate':
                base_probability += 0.10
            elif experience == 'beginner':
                base_probability -= 0.15

            # Adjust for capital adequacy
            capital = user_profile.get('capital', 10000)
            required_capital = self.goal_templates[goal]['capital_requirements']

            if capital >= required_capital * 2:
                base_probability += 0.15
            elif capital >= required_capital:
                base_probability += 0.05
            elif capital < required_capital:
                base_probability -= 0.20

            # Adjust for time commitment match
            time_available = user_profile.get('time_available', 'low')
            required_time = self.goal_templates[goal]['time_commitment']

            time_match_score = {
                ('high', 'high'): 0.10,
                ('moderate', 'moderate'): 0.05,
                ('low', 'low'): 0.05,
                ('high', 'low'): -0.15,
                ('low', 'high'): -0.10
            }

            base_probability += time_match_score.get((required_time, time_available), 0)

            return max(0.20, min(0.90, base_probability))  # Cap between 20% and 90%

        except Exception as e:
            self.logger.error(f"❌ [SUCCESS_PROB] Failed to calculate success probability: {e}")
            return 0.50

    def _generate_timeline(self, goal: str, user_profile: Dict[str, Any]) -> Dict[str, str]:
        """Generate timeline for achieving the goal"""
        try:
            experience = user_profile.get('experience', 'beginner')

            # Base timelines by goal
            base_timelines = {
                "daily_income": {
                    "learning_phase": "2-3 months",
                    "practice_phase": "3-6 months",
                    "live_trading": "6+ months",
                    "profitability": "12-18 months"
                },
                "weekly_profits": {
                    "learning_phase": "1-2 months",
                    "practice_phase": "2-4 months",
                    "live_trading": "4+ months",
                    "profitability": "8-12 months"
                },
                "monthly_growth": {
                    "learning_phase": "2-4 weeks",
                    "practice_phase": "1-2 months",
                    "live_trading": "2+ months",
                    "profitability": "6-9 months"
                },
                "wealth_building": {
                    "learning_phase": "1-2 weeks",
                    "practice_phase": "2-4 weeks",
                    "live_trading": "1+ months",
                    "profitability": "3-6 months"
                },
                "aggressive_growth": {
                    "learning_phase": "3-6 months",
                    "practice_phase": "6-12 months",
                    "live_trading": "12+ months",
                    "profitability": "18-24 months"
                }
            }

            timeline = base_timelines.get(goal, base_timelines["monthly_growth"])

            # Adjust for experience
            if experience == 'expert':
                # Reduce all phases by ~50%
                for phase in timeline:
                    if "months" in timeline[phase]:
                        # Simple reduction logic
                        timeline[phase] = timeline[phase].replace("12-18", "6-9").replace("8-12", "4-6")
            elif experience == 'beginner':
                # Increase learning phase
                timeline["learning_phase"] = timeline["learning_phase"].replace("1-2", "2-4").replace("2-3", "3-6")

            return timeline

        except Exception as e:
            self.logger.error(f"❌ [TIMELINE] Failed to generate timeline: {e}")
            return {"learning_phase": "1-3 months", "profitability": "6-12 months"}

    # ============================================================================
    # PLAN MANAGEMENT AND MONITORING
    # ============================================================================

    async def get_active_plans(self) -> List[Dict[str, Any]]:
        """Get all active trading plans"""
        try:
            active_plans_list = []

            for plan_id, plan in self.active_plans.items():
                plan_dict = {
                    "plan_id": plan_id,
                    "plan_name": getattr(plan, 'plan_name', f"Plan {plan_id}"),
                    "target_profit": plan.target.profit_target,
                    "timeframe_days": plan.target.timeframe_days,
                    "starting_capital": plan.target.starting_capital,
                    "risk_tolerance": plan.target.risk_tolerance,
                    "opportunities_count": len(plan.opportunities),
                    "status": getattr(plan, 'status', 'active'),
                    "created_at": plan.created_at.isoformat(),
                    "progress": await self._calculate_plan_progress(plan)
                }
                active_plans_list.append(plan_dict)

            return active_plans_list

        except Exception as e:
            self.logger.error(f"❌ [ACTIVE_PLANS] Failed to get active plans: {e}")
            return []

    async def get_plan_by_id(self, plan_id: str) -> Optional[Dict[str, Any]]:
        """Get specific trading plan by ID"""
        try:
            if plan_id not in self.active_plans:
                return None

            plan = self.active_plans[plan_id]

            # Convert to detailed dictionary
            plan_dict = {
                "plan_id": plan_id,
                "plan_name": getattr(plan, 'plan_name', f"Plan {plan_id}"),
                "target": {
                    "profit_target": plan.target.profit_target,
                    "timeframe_days": plan.target.timeframe_days,
                    "starting_capital": plan.target.starting_capital,
                    "risk_tolerance": plan.target.risk_tolerance
                },
                "opportunities": [
                    {
                        "symbol": opp.symbol,
                        "action": opp.action,
                        "entry_price": opp.entry_price,
                        "target_price": opp.target_price,
                        "stop_loss": opp.stop_loss,
                        "position_size": opp.position_size,
                        "confidence_score": opp.confidence_score,
                        "reasoning": opp.reasoning
                    }
                    for opp in plan.opportunities
                ],
                "portfolio_integration": {
                    "cash_allocation": plan.portfolio_integration.cash_allocation,
                    "risk_budget": plan.portfolio_integration.risk_budget
                },
                "scenarios": [
                    {
                        "name": scenario.name,
                        "description": scenario.description,
                        "probability": scenario.probability,
                        "expected_return": scenario.expected_return
                    }
                    for scenario in plan.scenarios
                ],
                "monitoring": {
                    "key_metrics": plan.monitoring.key_metrics,
                    "alert_conditions": plan.monitoring.alert_conditions,
                    "review_schedule": plan.monitoring.review_schedule
                },
                "created_at": plan.created_at.isoformat(),
                "status": getattr(plan, 'status', 'active'),
                "progress": await self._calculate_plan_progress(plan)
            }

            return plan_dict

        except Exception as e:
            self.logger.error(f"❌ [GET_PLAN] Failed to get plan {plan_id}: {e}")
            return None

    async def _calculate_plan_progress(self, plan: ComprehensiveTradingPlan) -> Dict[str, Any]:
        """Calculate progress metrics for a trading plan"""
        try:
            # In a real implementation, this would track actual executions
            # For now, simulate progress based on time elapsed

            days_elapsed = (datetime.now() - plan.created_at).days
            total_days = plan.target.timeframe_days
            time_progress = min(1.0, days_elapsed / total_days) if total_days > 0 else 0.0

            # Simulate execution progress (would be based on actual trades)
            opportunities_executed = int(len(plan.opportunities) * time_progress * 0.8)  # 80% execution rate

            return {
                "time_progress": time_progress,
                "days_elapsed": days_elapsed,
                "days_remaining": max(0, total_days - days_elapsed),
                "opportunities_executed": opportunities_executed,
                "opportunities_remaining": len(plan.opportunities) - opportunities_executed,
                "estimated_completion": "On track" if time_progress < 0.8 else "Behind schedule"
            }

        except Exception as e:
            self.logger.error(f"❌ [PROGRESS] Failed to calculate plan progress: {e}")
            return {"time_progress": 0.0, "opportunities_executed": 0}

    async def update_plan_status(self, plan_id: str, status: str) -> bool:
        """Update the status of a trading plan"""
        try:
            if plan_id not in self.active_plans:
                return False

            plan = self.active_plans[plan_id]
            old_status = getattr(plan, 'status', 'active')

            # Update status
            plan.status = status

            # If plan is completed or cancelled, move to history
            if status in ['completed', 'cancelled', 'failed']:
                if plan_id in self.active_plans:
                    del self.active_plans[plan_id]

                # Update execution stats
                if status == 'completed':
                    self.execution_stats["successful_plans"] += 1
                elif status == 'failed':
                    self.execution_stats["failed_plans"] += 1

            self.logger.info(f"📊 [STATUS] Plan {plan_id} status updated: {old_status} → {status}")
            return True

        except Exception as e:
            self.logger.error(f"❌ [UPDATE_STATUS] Failed to update plan status: {e}")
            return False

    def get_engine_status(self) -> Dict[str, Any]:
        """Get comprehensive engine status"""
        try:
            success_rate = (self.execution_stats["successful_plans"] /
                          max(1, self.execution_stats["total_plans"])) * 100

            return {
                "status": self.status.value if hasattr(self.status, 'value') else str(self.status),
                "plans_generated": self.plans_generated,
                "active_plans": len(self.active_plans),
                "total_plans": self.execution_stats["total_plans"],
                "successful_plans": self.execution_stats["successful_plans"],
                "failed_plans": self.execution_stats["failed_plans"],
                "success_rate": success_rate,
                "available_goals": list(self.goal_templates.keys()),
                "available_risk_levels": list(self.plan_templates.keys()),
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"❌ [ENGINE_STATUS] Failed to get engine status: {e}")
            return {"error": str(e)}

    def __str__(self) -> str:
        """String representation of the trading plans engine"""
        return f"AtlasTradingPlansEngine(status={self.status}, active_plans={len(self.active_plans)}, total_generated={self.plans_generated})"

    def __repr__(self) -> str:
        """Detailed representation of the trading plans engine"""
        return self.__str__()
