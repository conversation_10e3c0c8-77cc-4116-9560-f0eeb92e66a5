"""
A.T.L.A.S. Risk Core - Compatibility Bridge
This file provides backward compatibility by importing from the consolidated risk engine.
"""

import logging
import sys
import os

# Add consolidated path to imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'atlas_v5_consolidated', 'trading'))

logger = logging.getLogger(__name__)

# Import the consolidated risk engine implementation
try:
    from atlas_risk_engine import (
        AtlasRiskEngine,
        RiskLevel,
        RiskMetrics,
        PositionRisk,
        PortfolioRisk
    )
    
    logger.info("[BRIDGE] Successfully imported AtlasRiskEngine from consolidated engine")
    
except ImportError as e:
    logger.error(f"[BRIDGE] Failed to import from consolidated risk engine: {e}")
    
    # Fallback implementation for critical functionality
    from datetime import datetime
    from typing import Dict, List, Optional, Any
    from dataclasses import dataclass
    from enum import Enum
    
    class RiskLevel(Enum):
        """Risk level enumeration"""
        LOW = "low"
        MEDIUM = "medium"
        HIGH = "high"
        CRITICAL = "critical"
    
    @dataclass
    class RiskMetrics:
        """Risk metrics data structure"""
        symbol: str
        risk_level: RiskLevel
        volatility: float
        beta: float
        var_95: float  # Value at Risk 95%
        max_drawdown: float
        sharpe_ratio: float
        timestamp: datetime
        
    @dataclass
    class PositionRisk:
        """Position risk assessment"""
        symbol: str
        position_size: float
        market_value: float
        risk_amount: float
        risk_percentage: float
        stop_loss: Optional[float]
        risk_level: RiskLevel
        
    @dataclass
    class PortfolioRisk:
        """Portfolio-level risk assessment"""
        total_value: float
        total_risk: float
        risk_percentage: float
        diversification_score: float
        correlation_risk: float
        concentration_risk: float
        overall_risk_level: RiskLevel
    
    class AtlasRiskEngine:
        """Fallback implementation of AtlasRiskEngine"""
        
        def __init__(self):
            self.logger = logging.getLogger(__name__)
            self.risk_limits = {
                "max_position_risk": 0.02,  # 2% max risk per position
                "max_portfolio_risk": 0.10,  # 10% max portfolio risk
                "max_correlation": 0.7,     # Max correlation between positions
                "max_concentration": 0.20   # Max 20% in single position
            }
            self.logger.warning("[FALLBACK] Using fallback AtlasRiskEngine implementation")
        
        async def initialize(self):
            """Initialize the risk engine"""
            self.logger.info("[FALLBACK] Risk engine initialized in fallback mode")
            return True
        
        async def calculate_position_risk(self, symbol: str, position_size: float, entry_price: float, stop_loss: Optional[float] = None) -> PositionRisk:
            """Calculate risk for a position"""
            market_value = position_size * entry_price
            
            if stop_loss:
                risk_amount = abs(position_size * (entry_price - stop_loss))
                risk_percentage = risk_amount / market_value if market_value > 0 else 0
            else:
                # Default 2% risk if no stop loss
                risk_amount = market_value * 0.02
                risk_percentage = 0.02
            
            # Determine risk level
            if risk_percentage <= 0.01:
                risk_level = RiskLevel.LOW
            elif risk_percentage <= 0.02:
                risk_level = RiskLevel.MEDIUM
            elif risk_percentage <= 0.05:
                risk_level = RiskLevel.HIGH
            else:
                risk_level = RiskLevel.CRITICAL
            
            return PositionRisk(
                symbol=symbol,
                position_size=position_size,
                market_value=market_value,
                risk_amount=risk_amount,
                risk_percentage=risk_percentage,
                stop_loss=stop_loss,
                risk_level=risk_level
            )
        
        async def calculate_portfolio_risk(self, positions: List[PositionRisk]) -> PortfolioRisk:
            """Calculate portfolio-level risk"""
            if not positions:
                return PortfolioRisk(
                    total_value=0,
                    total_risk=0,
                    risk_percentage=0,
                    diversification_score=1.0,
                    correlation_risk=0,
                    concentration_risk=0,
                    overall_risk_level=RiskLevel.LOW
                )
            
            total_value = sum(pos.market_value for pos in positions)
            total_risk = sum(pos.risk_amount for pos in positions)
            risk_percentage = total_risk / total_value if total_value > 0 else 0
            
            # Simple diversification score (inverse of concentration)
            max_position_pct = max(pos.market_value / total_value for pos in positions) if total_value > 0 else 0
            diversification_score = 1.0 - max_position_pct
            
            # Concentration risk
            concentration_risk = max_position_pct
            
            # Overall risk level
            if risk_percentage <= 0.05:
                overall_risk_level = RiskLevel.LOW
            elif risk_percentage <= 0.10:
                overall_risk_level = RiskLevel.MEDIUM
            elif risk_percentage <= 0.20:
                overall_risk_level = RiskLevel.HIGH
            else:
                overall_risk_level = RiskLevel.CRITICAL
            
            return PortfolioRisk(
                total_value=total_value,
                total_risk=total_risk,
                risk_percentage=risk_percentage,
                diversification_score=diversification_score,
                correlation_risk=0.0,  # Simplified
                concentration_risk=concentration_risk,
                overall_risk_level=overall_risk_level
            )
        
        async def validate_trade(self, symbol: str, position_size: float, entry_price: float, stop_loss: Optional[float] = None) -> Dict[str, Any]:
            """Validate a trade against risk limits"""
            position_risk = await self.calculate_position_risk(symbol, position_size, entry_price, stop_loss)
            
            violations = []
            
            # Check position risk limit
            if position_risk.risk_percentage > self.risk_limits["max_position_risk"]:
                violations.append(f"Position risk {position_risk.risk_percentage:.2%} exceeds limit {self.risk_limits['max_position_risk']:.2%}")
            
            # Check if risk level is acceptable
            if position_risk.risk_level == RiskLevel.CRITICAL:
                violations.append("Position risk level is CRITICAL")
            
            return {
                "approved": len(violations) == 0,
                "violations": violations,
                "position_risk": position_risk,
                "risk_score": position_risk.risk_percentage
            }
        
        async def get_risk_metrics(self, symbol: str) -> RiskMetrics:
            """Get risk metrics for a symbol"""
            # Fallback metrics with default values
            return RiskMetrics(
                symbol=symbol,
                risk_level=RiskLevel.MEDIUM,
                volatility=0.20,  # 20% default volatility
                beta=1.0,         # Market beta
                var_95=0.05,      # 5% VaR
                max_drawdown=0.15, # 15% max drawdown
                sharpe_ratio=0.5,  # Moderate Sharpe ratio
                timestamp=datetime.now()
            )
        
        def get_risk_limits(self) -> Dict[str, float]:
            """Get current risk limits"""
            return self.risk_limits.copy()
        
        def update_risk_limits(self, new_limits: Dict[str, float]):
            """Update risk limits"""
            self.risk_limits.update(new_limits)
            self.logger.info(f"[FALLBACK] Risk limits updated: {new_limits}")
        
        def get_engine_status(self) -> Dict[str, Any]:
            """Get engine status"""
            return {
                "status": "fallback",
                "mode": "fallback",
                "risk_limits": self.risk_limits,
                "active": True
            }

# Export the main classes for compatibility
__all__ = [
    'AtlasRiskEngine',
    'RiskLevel',
    'RiskMetrics',
    'PositionRisk',
    'PortfolioRisk'
]
