import requests
import json
from datetime import datetime

print('=== Testing Atlas Trading Plan Generation ===')
print(f'Current Time: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

# Test the trading plan generation endpoint
try:
    # Create a test request for a high-risk trading plan
    plan_request = {
        'target_profit': 10.0,  # $10 target (smaller for testing)
        'timeframe_days': 1,       # 1 day (by market close)
        'starting_capital': 1000.0,  # $1,000 starting capital
        'risk_tolerance': 'aggressive'  # High-risk tolerance
    }
    
    print(f'📊 Requesting trading plan:')
    print(f'   Target Profit: ${plan_request["target_profit"]:,.0f}')
    print(f'   Timeframe: {plan_request["timeframe_days"]} day(s)')
    print(f'   Starting Capital: ${plan_request["starting_capital"]:,.0f}')
    print(f'   Risk Tolerance: {plan_request["risk_tolerance"]}')
    
    response = requests.post(
        'http://localhost:8002/api/v1/trading-plan/generate',
        json=plan_request,
        timeout=30
    )
    
    print(f'\n✅ Response Status: {response.status_code}')
    
    if response.status_code == 200:
        plan_data = response.json()
        print(f'\n🎯 Generated Trading Plan:')
        print(f'   Plan ID: {plan_data.get("plan_id", "Unknown")}')
        print(f'   Plan Name: {plan_data.get("plan_name", "Unknown")}')
        
        opportunities = plan_data.get('opportunities', [])
        print(f'   Trading Opportunities: {len(opportunities)}')
        
        if opportunities:
            print(f'\n📈 Top Opportunities:')
            for i, opp in enumerate(opportunities[:3], 1):
                symbol = opp.get('symbol', 'Unknown')
                entry_price = opp.get('entry_price', 0)
                target_price = opp.get('exit_target_price', 0)
                profit_potential = opp.get('max_profit_dollars', 0)
                confidence = opp.get('success_probability', 0) * 100
                
                print(f'   {i}. {symbol}: Entry ${entry_price:.2f} → Target ${target_price:.2f}')
                print(f'      Profit Potential: ${profit_potential:.0f} ({confidence:.1f}% confidence)')
        
        total_expected = plan_data.get('total_expected_return', 0)
        total_risk = plan_data.get('total_risk_amount', 0)
        plan_confidence = plan_data.get('plan_confidence', 0) * 100
        
        print(f'\n💰 Plan Summary:')
        print(f'   Expected Return: ${total_expected:.0f}')
        print(f'   Total Risk: ${total_risk:.0f}')
        print(f'   Plan Confidence: {plan_confidence:.1f}%')
        
        # Show available strategies and instruments
        portfolio_integration = plan_data.get('portfolio_integration', {})
        if portfolio_integration:
            print(f'\n🔧 Available Strategies:')
            strategies = portfolio_integration.get('strategy_recommendations', [])
            for strategy in strategies[:3]:
                print(f'   - {strategy}')
        
    else:
        print(f'❌ Error: {response.status_code}')
        print(f'Response: {response.text}')
        
except Exception as e:
    print(f'❌ Error: {e}')
