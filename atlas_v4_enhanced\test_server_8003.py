#!/usr/bin/env python3
"""
Test server on port 8003 to check for port conflicts
"""

import uvicorn
from fastapi import FastAP<PERSON>
from fastapi.responses import HTMLResponse, JSONResponse
import sys

print("🧪 Creating test server on port 8003...", flush=True)

app = FastAPI(title="A.T.L.A.S. Test Server - Port 8003")

@app.get("/")
async def root():
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>A.T.L.A.S. Test Server - Port 8003</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }
            .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .success { color: #28a745; font-weight: bold; }
            .info { color: #007bff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 A.T.L.A.S. Test Server - Port 8003</h1>
            <p class="success">✅ Server is running successfully on port 8003!</p>
            <p class="info">📡 This confirms the server setup works</p>
            <p class="info">🌐 Web interface is functional</p>
            <p>If you can see this, the basic server functionality is working. The issue with port 8002 might be a port conflict.</p>
            
            <h2>🔧 Status:</h2>
            <ul>
                <li>✅ FastAPI: Working</li>
                <li>✅ uvicorn: Working</li>
                <li>✅ Web interface: Working</li>
                <li>✅ Port 8003: Available</li>
            </ul>
            
            <p><strong>Next:</strong> We can start the full A.T.L.A.S. system on port 8003 instead of 8002.</p>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html)

@app.get("/test")
async def test():
    return JSONResponse(content={
        "status": "success",
        "message": "Test server working on port 8003",
        "port": 8003,
        "ready_for_atlas": True
    })

if __name__ == "__main__":
    print("🚀 Starting test server on http://localhost:8003", flush=True)
    print("✅ Testing if port 8003 works better than 8002", flush=True)
    
    try:
        uvicorn.run(app, host="0.0.0.0", port=8003, log_level="info")
    except Exception as e:
        print(f"❌ Error: {e}", flush=True)
        sys.exit(1)
