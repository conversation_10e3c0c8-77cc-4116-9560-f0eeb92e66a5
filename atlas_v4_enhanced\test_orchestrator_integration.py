#!/usr/bin/env python3
"""
Test the orchestrator integration with the Lee Method realtime scanner
"""

import asyncio
import sys
sys.path.append('.')

async def test_orchestrator_integration():
    print("🔍 TESTING ORCHESTRATOR INTEGRATION")
    print("=" * 50)
    
    try:
        # Step 1: Initialize orchestrator
        from atlas_orchestrator import AtlasOrchestrator
        
        print("🚀 Initializing orchestrator...")
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()
        
        print("✅ Orchestrator initialized")
        
        # Step 2: Check if Lee Method engine is the realtime scanner
        if 'lee_method' in orchestrator.engines:
            lee_engine = orchestrator.engines['lee_method']
            print(f"✅ Lee Method engine found: {type(lee_engine).__name__}")
            
            # Check if it has active_signals (realtime scanner feature)
            if hasattr(lee_engine, 'active_signals'):
                print("✅ Engine has active_signals storage (realtime scanner)")
                print(f"   Current active signals: {len(lee_engine.active_signals)}")
                
                # Check if it has scanning capability
                if hasattr(lee_engine, 'start_scanning'):
                    print("✅ Engine has scanning capability")
                    
                    # Check if scanning is already started
                    if hasattr(lee_engine, 'is_running') and lee_engine.is_running:
                        print("✅ Scanner is already running")
                    else:
                        print("⚠️  Scanner not running - starting it...")
                        await lee_engine.start_scanning()
                        print("✅ Scanner started")
                else:
                    print("❌ Engine missing scanning capability")
            else:
                print("❌ Engine missing active_signals storage")
        else:
            print("❌ Lee Method engine not found in orchestrator")
            return
        
        # Step 3: Test signal retrieval through orchestrator
        print(f"\n📋 TESTING ORCHESTRATOR SIGNAL RETRIEVAL:")
        signals_data = await orchestrator.get_lee_method_signals()
        signals = signals_data.get('signals', [])
        
        print(f"✅ Retrieved {len(signals)} signals through orchestrator")
        
        for i, signal in enumerate(signals[:3]):
            symbol = signal.get('symbol', 'UNKNOWN')
            confidence = signal.get('confidence', 0)
            description = signal.get('description', 'No description')
            print(f"   {i+1}. {symbol}: {confidence:.1%} - {description[:50]}...")
        
        # Step 4: Test caching
        print(f"\n💾 TESTING SIGNAL CACHING:")
        if hasattr(orchestrator, '_cached_lee_signals'):
            cached_count = len(orchestrator._cached_lee_signals) if orchestrator._cached_lee_signals else 0
            print(f"✅ Cached signals: {cached_count}")
        else:
            print("❌ No signal caching found")
        
        print(f"\n✅ ORCHESTRATOR INTEGRATION TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_orchestrator_integration())
