"""
A.T.L.A.S. Enhanced Market Data Manager
Multi-source data aggregation with intelligent fallback and caching
"""

import asyncio
import logging
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import pandas as pd
import numpy as np

# Optional yfinance import (graceful fallback)
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    yf = None

from atlas_rate_limiter import rate_limiter, APIProvider
from config import settings

logger = logging.getLogger(__name__)


@dataclass
class MarketQuote:
    """Standardized market quote"""
    symbol: str
    price: float
    change: float
    change_percent: float
    volume: int
    high: float
    low: float
    open: float
    timestamp: datetime
    source: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'price': self.price,
            'change': self.change,
            'change_percent': self.change_percent,
            'volume': self.volume,
            'high': self.high,
            'low': self.low,
            'open': self.open,
            'timestamp': self.timestamp.isoformat(),
            'source': self.source
        }


@dataclass
class HistoricalData:
    """Standardized historical data"""
    symbol: str
    data: pd.DataFrame
    timeframe: str
    source: str
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'symbol': self.symbol,
            'data': self.data.to_dict('records'),
            'timeframe': self.timeframe,
            'source': self.source,
            'timestamp': self.timestamp.isoformat()
        }


class EnhancedMarketDataManager:
    """Enhanced market data manager with multiple sources and intelligent fallback"""
    
    def __init__(self):
        self.data_sources = {
            'fmp': self._get_fmp_data,
            'alpaca': self._get_alpaca_data,
            'yfinance': self._get_yfinance_data,
            'polygon': self._get_polygon_data,
            'alpha_vantage': self._get_alpha_vantage_data,
            'iex': self._get_iex_data,
            'finnhub': self._get_finnhub_data,
            'twelve_data': self._get_twelve_data_data
        }
        
        # Enhanced source priority (higher number = higher priority)
        # Multiple data sources for redundancy and increased capacity
        self.source_priority = {
            'fmp': 10,           # Primary source - FMP API (paid tier)
            'polygon': 9,        # High priority - Polygon API (paid tier)
            'alpaca': 8,         # Secondary source - Alpaca API
            'finnhub': 7,        # Tertiary source - Finnhub API
            'alpha_vantage': 6,  # Quaternary source - Alpha Vantage
            'twelve_data': 5,    # Alternative source - Twelve Data
            'iex': 4,            # IEX Cloud API
            'yfinance': 3        # Fallback source - yfinance (free but limited)
        }
        
        # Source health tracking
        self.source_health = {source: 1.0 for source in self.data_sources}
        self.source_last_success = {source: time.time() for source in self.data_sources}
        self.source_failure_count = {source: 0 for source in self.data_sources}
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'cache_hits': 0,
            'fallback_usage': 0,
            'average_response_time': 0.0,
            'source_usage': {source: 0 for source in self.data_sources}
        }
        
        # Local cache for ultra-fast access
        self.quote_cache = {}
        self.historical_cache = {}
        self.cache_ttl = {}
        
        # Start rate limiter (only if event loop is running)
        try:
            loop = asyncio.get_running_loop()
            asyncio.create_task(rate_limiter.start_processing())
        except RuntimeError:
            # No event loop running, will start later when needed
            pass
    
    def _update_source_health(self, source: str, success: bool):
        """Update source health based on success/failure"""
        if success:
            self.source_health[source] = min(1.0, self.source_health[source] + 0.1)
            self.source_last_success[source] = time.time()
            self.source_failure_count[source] = 0
        else:
            self.source_health[source] = max(0.1, self.source_health[source] - 0.2)
            self.source_failure_count[source] += 1
    
    def _get_source_score(self, source: str) -> float:
        """Calculate source score based on priority and health"""
        base_score = self.source_priority[source]
        health_multiplier = self.source_health[source]
        
        # Penalize sources that haven't succeeded recently
        time_penalty = min(1.0, (time.time() - self.source_last_success[source]) / 3600)  # 1 hour
        
        return base_score * health_multiplier * (1 - time_penalty * 0.5)
    
    def _get_ordered_sources(self) -> List[str]:
        """Get data sources ordered by score (best first)"""
        sources = list(self.data_sources.keys())
        sources.sort(key=self._get_source_score, reverse=True)
        return sources
    
    async def _get_fmp_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from FMP API"""
        try:
            from config import get_api_config

            fmp_config = get_api_config('fmp')
            if not fmp_config or not fmp_config.get('api_key'):
                logger.warning("FMP API key not configured")
                return None

            import aiohttp

            if data_type == 'quote':
                url = f"https://financialmodelingprep.com/api/v3/quote/{symbol}"
                params = {'apikey': fmp_config['api_key']}

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data and len(data) > 0:
                                quote = data[0]
                                return {
                                    'symbol': quote.get('symbol'),
                                    'price': quote.get('price'),
                                    'change': quote.get('change'),
                                    'changesPercentage': quote.get('changesPercentage'),
                                    'timestamp': datetime.now().isoformat()
                                }

            elif data_type == 'historical':
                url = f"https://financialmodelingprep.com/api/v3/historical-price-full/{symbol}"
                params = {
                    'apikey': fmp_config['api_key'],
                    'timeseries': 30  # Last 30 days
                }

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data and 'historical' in data:
                                # Convert to DataFrame
                                df_data = []
                                for bar in data['historical'][:30]:  # Limit to 30 bars
                                    df_data.append({
                                        'date': pd.to_datetime(bar.get('date')),
                                        'open': bar.get('open'),
                                        'high': bar.get('high'),
                                        'low': bar.get('low'),
                                        'close': bar.get('close'),
                                        'volume': bar.get('volume')
                                    })

                                if df_data:
                                    df = pd.DataFrame(df_data)
                                    df.set_index('date', inplace=True)
                                    df.sort_index(inplace=True)

                                    return HistoricalData(
                                        symbol=symbol,
                                        data=df,
                                        timeframe='1d',
                                        source='fmp',
                                        timestamp=datetime.now()
                                    )

            return None

        except Exception as e:
            logger.error(f"FMP data error for {symbol}: {e}")
            return None
    
    async def _get_alpaca_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Alpaca API"""
        try:
            from config import get_api_config

            alpaca_config = get_api_config('alpaca')
            if not alpaca_config or not alpaca_config.get('api_key'):
                logger.warning("Alpaca API key not configured")
                return None

            # Use the market engine's Alpaca client
            from atlas_market_core import AtlasMarketEngine

            # This is a simplified approach - in practice we'd want to reuse the existing client
            if data_type == 'historical':
                # For historical data, we'll delegate to the market engine
                # This is a placeholder - the actual implementation would use the Alpaca client directly
                logger.info(f"Alpaca historical data requested for {symbol}")
                return None  # Will be handled by market engine methods

            return None

        except Exception as e:
            logger.error(f"Alpaca data error for {symbol}: {e}")
            return None
    
    async def _get_yfinance_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Yahoo Finance with aggressive rate limiting and graceful degradation"""
        try:
            cache_key = f"yfinance:{symbol}:{data_type}"

            # Check for very recent cache to avoid hitting rate limits
            if cache_key in self.quote_cache or cache_key in self.historical_cache:
                cached_time = self.cache_ttl.get(cache_key, 0)
                cache_duration = 300 if data_type == 'historical' else 120  # 5 min for historical, 2 min for quotes

                if time.time() - cached_time < cache_duration:
                    logger.debug(f"Using cached yfinance data for {symbol} (avoiding rate limits)")
                    if data_type == 'quote':
                        return self.quote_cache.get(cache_key)
                    else:
                        return self.historical_cache.get(cache_key)

            # Check if we've been rate limited recently
            rate_limit_key = f"yfinance_rate_limit"
            if rate_limit_key in self.cache_ttl:
                last_rate_limit = self.cache_ttl[rate_limit_key]
                if time.time() - last_rate_limit < 300:  # 5 minute cooldown after rate limit
                    logger.warning(f"YFinance rate limited recently, skipping {symbol}")
                    return None

            # Aggressive rate limiting - much longer delays
            await asyncio.sleep(5.0)  # 5 second delay between requests

            # Create ticker with timeout (if yfinance is available)
            if not YFINANCE_AVAILABLE:
                logger.warning(f"YFinance not available - cannot get data for {symbol}")
                return None

            ticker = yf.Ticker(symbol)

            if data_type == 'quote':
                logger.warning(f"YFinance rate limited - cannot get quote for {symbol}")
                return None

            elif data_type == 'historical':
                logger.info(f"Attempting to get historical data for {symbol}")

                try:
                    # Try to get historical data with very conservative approach
                    hist = ticker.history(period="30d", interval="1d", timeout=10)

                    if not hist.empty:
                        # Process the data
                        hist = hist.reset_index()
                        hist.columns = [col.lower() for col in hist.columns]

                        # Ensure required columns
                        required_columns = ['open', 'high', 'low', 'close', 'volume']
                        if all(col in hist.columns for col in required_columns):
                            historical = HistoricalData(
                                symbol=symbol,
                                data=hist,
                                timeframe='1d',
                                source='yfinance',
                                timestamp=datetime.now()
                            )

                            # Cache the result
                            self.historical_cache[cache_key] = historical
                            self.cache_ttl[cache_key] = time.time()

                            logger.info(f"Successfully got yfinance historical data for {symbol}")
                            return historical

                    # If we get here, no data available
                    logger.warning(f"No historical data available for {symbol}")
                    return None

                except Exception as hist_error:
                    if "429" in str(hist_error) or "Too Many Requests" in str(hist_error):
                        # Mark that we've been rate limited
                        self.cache_ttl[rate_limit_key] = time.time()
                        logger.error(f"YFinance rate limited for {symbol}: {hist_error}")
                    else:
                        logger.error(f"YFinance historical error for {symbol}: {hist_error}")

                    # Return None on error - no fake data
                    return None

            return None

        except Exception as e:
            if "429" in str(e) or "Too Many Requests" in str(e):
                # Mark that we've been rate limited
                rate_limit_key = f"yfinance_rate_limit"
                self.cache_ttl[rate_limit_key] = time.time()
                logger.error(f"YFinance rate limited for {symbol}: {e}")
            else:
                logger.error(f"YFinance data error for {symbol}: {e}")

            # Return None on error - no fake data
            return None
    
    async def _get_polygon_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Polygon.io - DISABLED due to API key requirements"""
        # Polygon requires valid API key - skip for now to avoid 404 errors
        logger.debug(f"Polygon data source disabled for {symbol} - requires valid API key")
        return None

    async def _get_alpha_vantage_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Alpha Vantage API"""
        try:
            api_key = getattr(settings, 'ALPHA_VANTAGE_API_KEY', None)
            if not api_key:
                logger.debug(f"Alpha Vantage API key not configured for {symbol}")
                return None

            import aiohttp

            if data_type == 'quote':
                url = "https://www.alphavantage.co/query"
                params = {
                    'function': 'GLOBAL_QUOTE',
                    'symbol': symbol,
                    'apikey': api_key
                }

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if 'Global Quote' in data:
                                quote_data = data['Global Quote']
                                return QuoteData(
                                    symbol=symbol,
                                    price=float(quote_data.get('05. price', 0)),
                                    change=float(quote_data.get('09. change', 0)),
                                    change_percent=float(quote_data.get('10. change percent', '0%').replace('%', '')),
                                    volume=int(quote_data.get('06. volume', 0)),
                                    timestamp=datetime.now(),
                                    source='alpha_vantage'
                                )

            return None

        except Exception as e:
            logger.error(f"Error getting Alpha Vantage data for {symbol}: {e}")
            return None

    async def _get_iex_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from IEX Cloud API"""
        try:
            api_key = getattr(settings, 'IEX_API_KEY', None)
            if not api_key:
                logger.debug(f"IEX API key not configured for {symbol}")
                return None

            import aiohttp

            if data_type == 'quote':
                url = f"https://cloud.iexapis.com/stable/stock/{symbol}/quote"
                params = {'token': api_key}

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            return QuoteData(
                                symbol=symbol,
                                price=float(data.get('latestPrice', 0)),
                                change=float(data.get('change', 0)),
                                change_percent=float(data.get('changePercent', 0)) * 100,
                                volume=int(data.get('latestVolume', 0)),
                                timestamp=datetime.now(),
                                source='iex'
                            )

            return None

        except Exception as e:
            logger.error(f"Error getting IEX data for {symbol}: {e}")
            return None

    async def _get_finnhub_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Finnhub API"""
        try:
            api_key = getattr(settings, 'FINNHUB_API_KEY', None)
            if not api_key:
                logger.debug(f"Finnhub API key not configured for {symbol}")
                return None

            import aiohttp

            if data_type == 'quote':
                url = f"https://finnhub.io/api/v1/quote"
                params = {
                    'symbol': symbol,
                    'token': api_key
                }

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if 'c' in data:  # Current price
                                return QuoteData(
                                    symbol=symbol,
                                    price=float(data.get('c', 0)),
                                    change=float(data.get('d', 0)),
                                    change_percent=float(data.get('dp', 0)),
                                    volume=0,  # Finnhub doesn't provide volume in quote
                                    timestamp=datetime.now(),
                                    source='finnhub'
                                )

            return None

        except Exception as e:
            logger.error(f"Error getting Finnhub data for {symbol}: {e}")
            return None

    async def _get_twelve_data_data(self, symbol: str, data_type: str = 'quote') -> Optional[Any]:
        """Get data from Twelve Data API"""
        try:
            api_key = getattr(settings, 'TWELVE_DATA_API_KEY', None)
            if not api_key:
                logger.debug(f"Twelve Data API key not configured for {symbol}")
                return None

            import aiohttp

            if data_type == 'quote':
                url = f"https://api.twelvedata.com/price"
                params = {
                    'symbol': symbol,
                    'apikey': api_key
                }

                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            if 'price' in data:
                                return QuoteData(
                                    symbol=symbol,
                                    price=float(data.get('price', 0)),
                                    change=0,  # Would need separate call for change
                                    change_percent=0,
                                    volume=0,  # Would need separate call for volume
                                    timestamp=datetime.now(),
                                    source='twelve_data'
                                )

            return None

        except Exception as e:
            logger.error(f"Error getting Twelve Data for {symbol}: {e}")
            return None
    
    async def get_quote(self, symbol: str, timeout: float = 10.0) -> Optional[MarketQuote]:
        """Get real-time quote with intelligent fallback"""
        start_time = time.time()
        self.metrics['total_requests'] += 1
        
        # Try sources in order of preference
        sources = self._get_ordered_sources()
        
        for source in sources:
            try:
                logger.debug(f"Trying {source} for {symbol} quote")
                
                result = await asyncio.wait_for(
                    self.data_sources[source](symbol, 'quote'),
                    timeout=timeout / len(sources)
                )
                
                if result:
                    self._update_source_health(source, True)
                    self.metrics['successful_requests'] += 1
                    self.metrics['source_usage'][source] += 1
                    
                    response_time = time.time() - start_time
                    self.metrics['average_response_time'] = (
                        (self.metrics['average_response_time'] * (self.metrics['successful_requests'] - 1) + response_time) /
                        self.metrics['successful_requests']
                    )
                    
                    logger.info(f"Successfully got {symbol} quote from {source} in {response_time:.2f}s")
                    return result
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout getting {symbol} quote from {source}")
                self._update_source_health(source, False)
                continue
            
            except Exception as e:
                logger.error(f"Error getting {symbol} quote from {source}: {e}")
                self._update_source_health(source, False)
                continue
        
        logger.error(f"Failed to get quote for {symbol} from all sources")
        return None
    
    async def get_historical_data(self, symbol: str, timeframe: str = '1d',
                                 timeout: float = 30.0) -> Optional[HistoricalData]:
        """Get historical data with intelligent fallback"""
        start_time = time.time()
        
        # Try sources in order of preference
        sources = self._get_ordered_sources()
        
        for source in sources:
            try:
                logger.debug(f"Trying {source} for {symbol} historical data")
                
                # Give each source a reasonable timeout (minimum 10 seconds)
                source_timeout = max(10.0, timeout / len(sources))
                result = await asyncio.wait_for(
                    self.data_sources[source](symbol, 'historical'),
                    timeout=source_timeout
                )
                
                if result:
                    self._update_source_health(source, True)
                    self.metrics['source_usage'][source] += 1
                    
                    response_time = time.time() - start_time
                    logger.info(f"Successfully got {symbol} historical data from {source} in {response_time:.2f}s")
                    return result
                
            except asyncio.TimeoutError:
                logger.warning(f"Timeout getting {symbol} historical data from {source}")
                self._update_source_health(source, False)
                continue
            
            except Exception as e:
                logger.error(f"Error getting {symbol} historical data from {source}: {e}")
                self._update_source_health(source, False)
                continue
        
        logger.error(f"Failed to get historical data for {symbol} from all sources")
        return None
    
    async def get_multiple_quotes(self, symbols: List[str], timeout: float = 30.0) -> Dict[str, Optional[MarketQuote]]:
        """Get multiple quotes efficiently with batch processing"""
        start_time = time.time()
        results = {}
        
        # Create tasks for all symbols
        tasks = []
        for symbol in symbols:
            task = asyncio.create_task(self.get_quote(symbol, timeout / len(symbols)))
            tasks.append((symbol, task))
        
        # Wait for all tasks with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*[task for _, task in tasks], return_exceptions=True),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            logger.warning("Timeout in batch quote request")
        
        # Collect results
        for symbol, task in tasks:
            try:
                if task.done():
                    result = task.result()
                    results[symbol] = result
                else:
                    task.cancel()
                    results[symbol] = None
            except Exception as e:
                logger.error(f"Error getting quote for {symbol}: {e}")
                results[symbol] = None
        
        response_time = time.time() - start_time
        successful_count = len([r for r in results.values() if r is not None])
        
        logger.info(f"Batch quote request completed: {successful_count}/{len(symbols)} successful in {response_time:.2f}s")
        
        return results
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return {
            **self.metrics,
            'source_health': self.source_health,
            'source_priority': self.source_priority,
            'cache_size': len(self.quote_cache) + len(self.historical_cache)
        }


# Global enhanced market data manager
enhanced_market_data = EnhancedMarketDataManager()
