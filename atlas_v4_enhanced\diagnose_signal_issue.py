#!/usr/bin/env python3
"""
Diagnostic script to identify the signal detection and display issue
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from atlas_orchestrator import AtlasOrchestrator
from atlas_lee_method import get_scanner_instance
import atlas_server

async def diagnose_signal_issue():
    """Diagnose the signal detection and display issue"""
    
    print("🔍 ATLAS Signal Detection Diagnostic")
    print("=" * 50)
    
    # 1. Check if global realtime scanner exists and has signals
    print("\n1. Checking global realtime scanner...")
    if hasattr(atlas_server, 'realtime_scanner') and atlas_server.realtime_scanner:
        scanner = atlas_server.realtime_scanner
        print(f"   ✅ Global realtime scanner exists: {type(scanner).__name__}")
        
        # Check methods
        methods = ['get_active_results', 'get_latest_signals', 'get_active_signals']
        for method in methods:
            has_method = hasattr(scanner, method)
            print(f"   {'✅' if has_method else '❌'} Has {method}: {has_method}")
        
        # Check active signals
        if hasattr(scanner, 'active_signals'):
            active_count = len(scanner.active_signals) if scanner.active_signals else 0
            print(f"   📊 Active signals count: {active_count}")
            
            if active_count > 0:
                print("   📋 Active signals:")
                for symbol, signal in list(scanner.active_signals.items())[:5]:
                    print(f"      - {symbol}: {signal.confidence:.1%} confidence")
        
        # Check active results
        if hasattr(scanner, 'active_results'):
            results_count = len(scanner.active_results) if scanner.active_results else 0
            print(f"   📊 Active results count: {results_count}")
    else:
        print("   ❌ No global realtime scanner found")
    
    # 2. Check orchestrator's lee_method engine
    print("\n2. Checking orchestrator's lee_method engine...")
    try:
        # Create orchestrator instance (this might be the issue)
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()
        
        if 'lee_method' in orchestrator.engines:
            lee_engine = orchestrator.engines['lee_method']
            print(f"   ✅ Lee Method engine exists: {type(lee_engine).__name__}")
            
            # Check methods
            methods = ['get_active_results', 'get_latest_signals', 'get_active_signals']
            for method in methods:
                has_method = hasattr(lee_engine, method)
                print(f"   {'✅' if has_method else '❌'} Has {method}: {has_method}")
            
            # Check if it's the same instance as global scanner
            if hasattr(atlas_server, 'realtime_scanner') and atlas_server.realtime_scanner:
                is_same = lee_engine is atlas_server.realtime_scanner
                print(f"   {'✅' if is_same else '❌'} Same as global scanner: {is_same}")
            
            # Try to get signals
            try:
                signals_data = await orchestrator.get_lee_method_signals()
                signals = signals_data.get('signals', [])
                print(f"   📊 Retrieved signals count: {len(signals)}")
                
                if len(signals) > 0:
                    print("   📋 Retrieved signals:")
                    for signal in signals[:3]:
                        symbol = signal.get('symbol', 'UNKNOWN')
                        confidence = signal.get('confidence', 0)
                        print(f"      - {symbol}: {confidence:.1%} confidence")
                        
            except Exception as e:
                print(f"   ❌ Error retrieving signals: {e}")
                
        else:
            print("   ❌ No lee_method engine in orchestrator")
            
    except Exception as e:
        print(f"   ❌ Error creating orchestrator: {e}")
    
    # 3. Check scanner instance function
    print("\n3. Checking scanner instance function...")
    try:
        scanner_instance = get_scanner_instance()
        print(f"   ✅ Scanner instance created: {type(scanner_instance).__name__}")
        
        # Check methods
        methods = ['get_active_results', 'get_latest_signals', 'get_active_signals']
        for method in methods:
            has_method = hasattr(scanner_instance, method)
            print(f"   {'✅' if has_method else '❌'} Has {method}: {has_method}")
        
        # Check active signals
        if hasattr(scanner_instance, 'active_signals'):
            active_count = len(scanner_instance.active_signals) if scanner_instance.active_signals else 0
            print(f"   📊 Active signals count: {active_count}")
            
        # Check if it's the same as global scanner
        if hasattr(atlas_server, 'realtime_scanner') and atlas_server.realtime_scanner:
            is_same = scanner_instance is atlas_server.realtime_scanner
            print(f"   {'✅' if is_same else '❌'} Same as global scanner: {is_same}")
            
    except Exception as e:
        print(f"   ❌ Error with scanner instance: {e}")
    
    print("\n" + "=" * 50)
    print("🔍 Diagnostic completed")

if __name__ == "__main__":
    asyncio.run(diagnose_signal_issue())
