#!/usr/bin/env python3
"""
Test the complete Lee Method signal pipeline:
1. Signal detection
2. Signal storage in active_signals
3. API endpoint retrieval
4. Desktop notification
"""

import asyncio
import sys
import requests
import json
from datetime import datetime
sys.path.append('.')

async def test_signal_pipeline():
    print("🔍 TESTING LEE METHOD SIGNAL PIPELINE")
    print("=" * 50)
    
    try:
        # Step 1: Initialize the Lee Method realtime scanner
        from atlas_lee_method import AtlasLeeMethodRealtimeScanner
        from atlas_market_core import AtlasMarketEngine
        from config import get_api_config
        
        fmp_config = get_api_config('fmp')
        fmp_api_key = fmp_config.get('api_key') if fmp_config else None
        
        if not fmp_api_key:
            print("❌ No FMP API key found")
            return
        
        print("✅ FMP API key found")
        
        # Initialize market engine and scanner
        market_engine = AtlasMarketEngine()
        scanner = AtlasLeeMethodRealtimeScanner(fmp_api_key, market_engine)
        
        print("✅ Lee Method realtime scanner initialized")
        
        # Step 2: Test signal detection on a few symbols
        test_symbols = ['AAPL', 'TSLA', 'PLTR', 'NVDA', 'SPY']
        print(f"\n🔍 Testing signal detection on {len(test_symbols)} symbols...")
        
        signals_found = 0
        for symbol in test_symbols:
            try:
                # Use the main scanner to detect signals
                from atlas_lee_method import LeeMethodScanner
                main_scanner = LeeMethodScanner(fmp_api_key, market_engine)
                
                signal = await main_scanner.scan_symbol(symbol)
                if signal:
                    signals_found += 1
                    print(f"✅ {symbol}: Signal found ({signal.confidence:.1%} confidence)")
                    
                    # Store in realtime scanner's active_signals
                    scanner.active_signals[symbol] = signal
                    
                    # Test desktop notification for high-confidence signals
                    if signal.confidence >= 0.75:
                        print(f"🔔 Triggering desktop notification for {symbol}")
                        await scanner._send_desktop_notification(signal)
                else:
                    print(f"❌ {symbol}: No signal")
                    
            except Exception as e:
                print(f"❌ {symbol}: Error - {e}")
        
        print(f"\n📊 DETECTION RESULTS:")
        print(f"   Signals Found: {signals_found}/{len(test_symbols)}")
        print(f"   Active Signals in Scanner: {len(scanner.active_signals)}")
        
        # Step 3: Test signal retrieval from scanner
        print(f"\n📋 TESTING SIGNAL RETRIEVAL:")
        latest_signals = scanner.get_latest_signals(limit=10)
        print(f"   Retrieved {len(latest_signals)} signals from scanner")
        
        for i, signal in enumerate(latest_signals[:3]):
            symbol = signal.get('symbol', 'UNKNOWN')
            confidence = signal.get('confidence', 0)
            description = signal.get('description', 'No description')
            print(f"   {i+1}. {symbol}: {confidence:.1%} - {description[:50]}...")
        
        # Step 4: Test API endpoints (if server is running)
        print(f"\n🌐 TESTING API ENDPOINTS:")
        try:
            # Test fast API endpoint
            response = requests.get('http://localhost:8002/api/v1/lee_method/signals/active', timeout=5)
            if response.status_code == 200:
                data = response.json()
                api_signals = data.get('signals', [])
                print(f"✅ Fast API: {len(api_signals)} active signals")
                
                for signal in api_signals[:2]:
                    symbol = signal.get('symbol', 'UNKNOWN')
                    confidence = signal.get('confidence', 0)
                    print(f"   - {symbol}: {confidence:.1%}")
            else:
                print(f"❌ Fast API: Status {response.status_code}")
                
        except Exception as e:
            print(f"❌ API Test failed: {e}")
            print("   (Server may not be running - this is expected if testing standalone)")
        
        print(f"\n✅ PIPELINE TEST COMPLETED")
        print(f"   Desktop notifications: {'✅ Working' if signals_found > 0 else '❌ No signals to test'}")
        print(f"   Signal storage: {'✅ Working' if len(scanner.active_signals) > 0 else '❌ No signals stored'}")
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_signal_pipeline())
