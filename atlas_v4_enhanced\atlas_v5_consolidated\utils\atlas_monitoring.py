"""
A.T.L.A.S. Monitoring and Metrics System
Comprehensive monitoring, metrics collection, and observability for the multi-agent system
"""

import asyncio
import logging
import json
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import uuid

# Prometheus imports
try:
    from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server, CollectorRegistry, REGISTRY
    from prometheus_client.core import CounterMetricFamily, GaugeMetricFamily, HistogramMetricFamily
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

    # Create dummy classes for when prometheus is not available
    class CollectorRegistry:
        def __init__(self, *args, **kwargs): pass

    class Counter:
        def __init__(self, *args, **kwargs): pass
        def inc(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self

    class Histogram:
        def __init__(self, *args, **kwargs): pass
        def observe(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self

    class Gauge:
        def __init__(self, *args, **kwargs): pass
        def set(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self

    class Info:
        def __init__(self, *args, **kwargs): pass
        def info(self, *args, **kwargs): pass
        def labels(self, *args, **kwargs): return self

    class CounterMetricFamily:
        def __init__(self, *args, **kwargs): pass

    class GaugeMetricFamily:
        def __init__(self, *args, **kwargs): pass

    class HistogramMetricFamily:
        def __init__(self, *args, **kwargs): pass

    def start_http_server(*args, **kwargs): pass

    REGISTRY = None

# Core imports
from models import EngineStatus

logger = logging.getLogger(__name__)

# ============================================================================
# MONITORING ENUMS AND MODELS
# ============================================================================

class MetricType(Enum):
    """Types of metrics"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"

class HealthStatus(Enum):
    """Health check status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class MetricPoint:
    """Individual metric data point"""
    name: str
    value: float
    labels: Dict[str, str] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class HealthCheck:
    """Health check result"""
    component: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    response_time_ms: float = 0.0

@dataclass
class Alert:
    """System alert"""
    alert_id: str
    component: str
    severity: AlertSeverity
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolved_at: Optional[datetime] = None

# ============================================================================
# PROMETHEUS METRICS COLLECTOR
# ============================================================================

class PrometheusMetricsCollector:
    """Prometheus metrics collector for multi-agent system"""

    _instance = None
    _initialized = False

    def __new__(cls, registry: Optional[CollectorRegistry] = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, registry: Optional[CollectorRegistry] = None):
        if self._initialized:
            return

        self.registry = registry or REGISTRY
        self.enabled = PROMETHEUS_AVAILABLE
        self.metrics_initialized = False

        if not self.enabled:
            logger.warning("Prometheus client not available - metrics will be collected but not exported")
            return

        # Initialize Prometheus metrics
        try:
            self._init_prometheus_metrics()
            self.metrics_initialized = True
            self._initialized = True
        except Exception as e:
            logger.warning(f"Failed to initialize Prometheus metrics: {e}")
            self.enabled = False

        logger.info("Prometheus metrics collector initialized")
    
    def _init_prometheus_metrics(self):
        """Initialize Prometheus metrics"""
        if not self.enabled:
            return
        
        # Agent metrics
        self.agent_requests_total = Counter(
            'atlas_agent_requests_total',
            'Total number of requests processed by agents',
            ['agent_role', 'status'],
            registry=self.registry
        )
        
        self.agent_request_duration = Histogram(
            'atlas_agent_request_duration_seconds',
            'Time spent processing agent requests',
            ['agent_role'],
            registry=self.registry
        )
        
        self.agent_confidence_score = Gauge(
            'atlas_agent_confidence_score',
            'Current confidence score of agents',
            ['agent_role'],
            registry=self.registry
        )
        
        # Orchestrator metrics
        self.orchestration_requests_total = Counter(
            'atlas_orchestration_requests_total',
            'Total orchestration requests',
            ['intent', 'mode', 'status'],
            registry=self.registry
        )
        
        self.orchestration_duration = Histogram(
            'atlas_orchestration_duration_seconds',
            'Time spent on orchestration requests',
            ['intent', 'mode'],
            registry=self.registry
        )
        
        # System metrics
        self.system_cpu_usage = Gauge(
            'atlas_system_cpu_usage_percent',
            'System CPU usage percentage',
            registry=self.registry
        )
        
        self.system_memory_usage = Gauge(
            'atlas_system_memory_usage_bytes',
            'System memory usage in bytes',
            registry=self.registry
        )
        
        self.active_sessions = Gauge(
            'atlas_active_sessions',
            'Number of active user sessions',
            registry=self.registry
        )
        
        # Trading metrics
        self.trading_signals_total = Counter(
            'atlas_trading_signals_total',
            'Total trading signals generated',
            ['symbol', 'signal_strength', 'status'],
            registry=self.registry
        )
        
        self.compliance_checks_total = Counter(
            'atlas_compliance_checks_total',
            'Total compliance checks performed',
            ['status'],
            registry=self.registry
        )
        
        # Health metrics
        self.component_health = Gauge(
            'atlas_component_health',
            'Health status of system components (1=healthy, 0=unhealthy)',
            ['component'],
            registry=self.registry
        )
    
    def record_agent_request(self, agent_role: str, status: str, duration: float, confidence: float):
        """Record agent request metrics"""
        if not self.enabled or not self.metrics_initialized:
            return

        try:
            self.agent_requests_total.labels(agent_role=agent_role, status=status).inc()
            self.agent_request_duration.labels(agent_role=agent_role).observe(duration)
            self.agent_confidence_score.labels(agent_role=agent_role).set(confidence)
        except Exception as e:
            logger.error(f"Failed to record agent metrics: {e}")
    
    def record_orchestration_request(self, intent: str, mode: str, status: str, duration: float):
        """Record orchestration request metrics"""
        if not self.enabled:
            return
        
        self.orchestration_requests_total.labels(intent=intent, mode=mode, status=status).inc()
        self.orchestration_duration.labels(intent=intent, mode=mode).observe(duration)
    
    def update_system_metrics(self, cpu_percent: float, memory_bytes: int, active_sessions: int):
        """Update system resource metrics"""
        if not self.enabled:
            return
        
        self.system_cpu_usage.set(cpu_percent)
        self.system_memory_usage.set(memory_bytes)
        self.active_sessions.set(active_sessions)
    
    def record_trading_signal(self, symbol: str, signal_strength: str, status: str):
        """Record trading signal metrics"""
        if not self.enabled:
            return
        
        self.trading_signals_total.labels(symbol=symbol, signal_strength=signal_strength, status=status).inc()
    
    def record_compliance_check(self, status: str):
        """Record compliance check metrics"""
        if not self.enabled:
            return
        
        self.compliance_checks_total.labels(status=status).inc()
    
    def update_component_health(self, component: str, is_healthy: bool):
        """Update component health metrics"""
        if not self.enabled:
            return
        
        self.component_health.labels(component=component).set(1.0 if is_healthy else 0.0)

# ============================================================================
# PERFORMANCE MONITOR
# ============================================================================

class PerformanceMonitor:
    """System performance monitoring"""
    
    def __init__(self, collection_interval: int = 30):
        self.collection_interval = collection_interval
        self.is_running = False
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.collection_thread = None
        
        # Performance thresholds
        self.thresholds = {
            "cpu_usage_warning": 80.0,
            "cpu_usage_critical": 95.0,
            "memory_usage_warning": 80.0,
            "memory_usage_critical": 95.0,
            "response_time_warning": 5.0,
            "response_time_critical": 10.0
        }
        
        logger.info("Performance monitor initialized")
    
    def start_monitoring(self):
        """Start performance monitoring"""
        if self.is_running:
            logger.warning("Performance monitoring already running")
            return
        
        self.is_running = True
        self.collection_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.collection_thread.start()
        
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.is_running = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        
        logger.info("Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_running:
            try:
                self._collect_system_metrics()
                time.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.collection_interval)
    
    def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics_history["cpu_usage"].append({
                "value": cpu_percent,
                "timestamp": datetime.now()
            })
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_bytes = memory.used
            
            self.metrics_history["memory_usage_percent"].append({
                "value": memory_percent,
                "timestamp": datetime.now()
            })
            
            self.metrics_history["memory_usage_bytes"].append({
                "value": memory_bytes,
                "timestamp": datetime.now()
            })
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            self.metrics_history["disk_usage_percent"].append({
                "value": disk_percent,
                "timestamp": datetime.now()
            })
            
            # Network metrics
            network = psutil.net_io_counters()
            self.metrics_history["network_bytes_sent"].append({
                "value": network.bytes_sent,
                "timestamp": datetime.now()
            })
            
            self.metrics_history["network_bytes_recv"].append({
                "value": network.bytes_recv,
                "timestamp": datetime.now()
            })
            
            # Check thresholds and generate alerts
            self._check_performance_thresholds(cpu_percent, memory_percent)
            
        except Exception as e:
            logger.error(f"Failed to collect system metrics: {e}")
    
    def _check_performance_thresholds(self, cpu_percent: float, memory_percent: float):
        """Check performance thresholds and generate alerts"""
        alerts = []
        
        # CPU threshold checks
        if cpu_percent >= self.thresholds["cpu_usage_critical"]:
            alerts.append(("cpu_usage", "critical", f"CPU usage critical: {cpu_percent:.1f}%"))
        elif cpu_percent >= self.thresholds["cpu_usage_warning"]:
            alerts.append(("cpu_usage", "warning", f"CPU usage high: {cpu_percent:.1f}%"))
        
        # Memory threshold checks
        if memory_percent >= self.thresholds["memory_usage_critical"]:
            alerts.append(("memory_usage", "critical", f"Memory usage critical: {memory_percent:.1f}%"))
        elif memory_percent >= self.thresholds["memory_usage_warning"]:
            alerts.append(("memory_usage", "warning", f"Memory usage high: {memory_percent:.1f}%"))
        
        # Log alerts
        for component, severity, message in alerts:
            if severity == "critical":
                logger.critical(f"Performance Alert - {message}")
            else:
                logger.warning(f"Performance Alert - {message}")
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        try:
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            network = psutil.net_io_counters()
            
            return {
                "cpu": {
                    "usage_percent": cpu_percent,
                    "count": psutil.cpu_count(),
                    "count_logical": psutil.cpu_count(logical=True)
                },
                "memory": {
                    "total_bytes": memory.total,
                    "used_bytes": memory.used,
                    "available_bytes": memory.available,
                    "usage_percent": memory.percent
                },
                "disk": {
                    "total_bytes": disk.total,
                    "used_bytes": disk.used,
                    "free_bytes": disk.free,
                    "usage_percent": (disk.used / disk.total) * 100
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get current metrics: {e}")
            return {"error": str(e)}
    
    def get_metrics_history(self, metric_name: str, hours_back: int = 1) -> List[Dict[str, Any]]:
        """Get historical metrics for a specific metric"""
        try:
            if metric_name not in self.metrics_history:
                return []
            
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            filtered_metrics = [
                metric for metric in self.metrics_history[metric_name]
                if metric["timestamp"] >= cutoff_time
            ]
            
            return filtered_metrics
            
        except Exception as e:
            logger.error(f"Failed to get metrics history: {e}")
            return []

# ============================================================================
# HEALTH CHECK SYSTEM
# ============================================================================

class HealthCheckSystem:
    """Comprehensive health checking for all system components"""

    def __init__(self):
        self.health_checks: Dict[str, Callable] = {}
        self.health_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.check_interval = 60  # 1 minute
        self.is_running = False
        self.check_thread = None

        # Register default health checks
        self._register_default_checks()

        logger.info("Health check system initialized")

    def _register_default_checks(self):
        """Register default health checks"""
        self.register_health_check("system_resources", self._check_system_resources)
        self.register_health_check("disk_space", self._check_disk_space)
        self.register_health_check("network_connectivity", self._check_network_connectivity)

    def register_health_check(self, component: str, check_function: Callable) -> bool:
        """Register a health check function"""
        try:
            self.health_checks[component] = check_function
            logger.info(f"Registered health check for component: {component}")
            return True
        except Exception as e:
            logger.error(f"Failed to register health check for {component}: {e}")
            return False

    def start_health_monitoring(self):
        """Start health check monitoring"""
        if self.is_running:
            logger.warning("Health monitoring already running")
            return

        self.is_running = True
        self.check_thread = threading.Thread(target=self._health_check_loop, daemon=True)
        self.check_thread.start()

        logger.info("Health check monitoring started")

    def stop_health_monitoring(self):
        """Stop health check monitoring"""
        self.is_running = False
        if self.check_thread:
            self.check_thread.join(timeout=5)

        logger.info("Health check monitoring stopped")

    def _health_check_loop(self):
        """Main health check loop"""
        while self.is_running:
            try:
                self.run_all_health_checks()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"Error in health check loop: {e}")
                time.sleep(self.check_interval)

    def run_all_health_checks(self) -> Dict[str, HealthCheck]:
        """Run all registered health checks"""
        results = {}

        for component, check_function in self.health_checks.items():
            try:
                start_time = time.time()
                result = check_function()
                end_time = time.time()

                if isinstance(result, HealthCheck):
                    result.response_time_ms = (end_time - start_time) * 1000
                    results[component] = result
                else:
                    # Convert simple result to HealthCheck
                    status = HealthStatus.HEALTHY if result else HealthStatus.UNHEALTHY
                    results[component] = HealthCheck(
                        component=component,
                        status=status,
                        message="Health check completed",
                        response_time_ms=(end_time - start_time) * 1000
                    )

                # Store in history
                self.health_history[component].append(results[component])

            except Exception as e:
                logger.error(f"Health check failed for {component}: {e}")
                results[component] = HealthCheck(
                    component=component,
                    status=HealthStatus.UNHEALTHY,
                    message=f"Health check failed: {e}",
                    details={"error": str(e)}
                )

        return results

    def _check_system_resources(self) -> HealthCheck:
        """Check system resource health"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            status = HealthStatus.HEALTHY
            messages = []

            if cpu_percent > 90:
                status = HealthStatus.UNHEALTHY
                messages.append(f"High CPU usage: {cpu_percent:.1f}%")
            elif cpu_percent > 80:
                status = HealthStatus.DEGRADED
                messages.append(f"Elevated CPU usage: {cpu_percent:.1f}%")

            if memory.percent > 90:
                status = HealthStatus.UNHEALTHY
                messages.append(f"High memory usage: {memory.percent:.1f}%")
            elif memory.percent > 80:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                messages.append(f"Elevated memory usage: {memory.percent:.1f}%")

            message = "; ".join(messages) if messages else "System resources healthy"

            return HealthCheck(
                component="system_resources",
                status=status,
                message=message,
                details={
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3)
                }
            )

        except Exception as e:
            return HealthCheck(
                component="system_resources",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check system resources: {e}"
            )

    def _check_disk_space(self) -> HealthCheck:
        """Check disk space health"""
        try:
            disk = psutil.disk_usage('/')
            usage_percent = (disk.used / disk.total) * 100

            status = HealthStatus.HEALTHY
            if usage_percent > 95:
                status = HealthStatus.UNHEALTHY
            elif usage_percent > 85:
                status = HealthStatus.DEGRADED

            return HealthCheck(
                component="disk_space",
                status=status,
                message=f"Disk usage: {usage_percent:.1f}%",
                details={
                    "usage_percent": usage_percent,
                    "free_gb": disk.free / (1024**3),
                    "total_gb": disk.total / (1024**3)
                }
            )

        except Exception as e:
            return HealthCheck(
                component="disk_space",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check disk space: {e}"
            )

    def _check_network_connectivity(self) -> HealthCheck:
        """Check network connectivity health"""
        try:
            import socket

            # Test connectivity to a reliable external service
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('8.8.8.8', 53))  # Google DNS
            sock.close()

            if result == 0:
                status = HealthStatus.HEALTHY
                message = "Network connectivity healthy"
            else:
                status = HealthStatus.UNHEALTHY
                message = "Network connectivity issues detected"

            return HealthCheck(
                component="network_connectivity",
                status=status,
                message=message,
                details={"test_result": result}
            )

        except Exception as e:
            return HealthCheck(
                component="network_connectivity",
                status=HealthStatus.UNHEALTHY,
                message=f"Failed to check network connectivity: {e}"
            )

    def get_overall_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            health_results = self.run_all_health_checks()

            # Calculate overall status
            statuses = [check.status for check in health_results.values()]

            if any(status == HealthStatus.UNHEALTHY for status in statuses):
                overall_status = HealthStatus.UNHEALTHY
            elif any(status == HealthStatus.DEGRADED for status in statuses):
                overall_status = HealthStatus.DEGRADED
            else:
                overall_status = HealthStatus.HEALTHY

            # Calculate health score (0-100)
            health_score = 0
            for status in statuses:
                if status == HealthStatus.HEALTHY:
                    health_score += 100
                elif status == HealthStatus.DEGRADED:
                    health_score += 60
                elif status == HealthStatus.UNHEALTHY:
                    health_score += 20
                # UNKNOWN gets 0 points

            health_score = health_score / len(statuses) if statuses else 0

            return {
                "overall_status": overall_status.value,
                "health_score": round(health_score, 1),
                "components_checked": len(health_results),
                "healthy_components": len([s for s in statuses if s == HealthStatus.HEALTHY]),
                "degraded_components": len([s for s in statuses if s == HealthStatus.DEGRADED]),
                "unhealthy_components": len([s for s in statuses if s == HealthStatus.UNHEALTHY]),
                "component_details": {
                    component: {
                        "status": check.status.value,
                        "message": check.message,
                        "response_time_ms": check.response_time_ms
                    }
                    for component, check in health_results.items()
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get overall health: {e}")
            return {
                "overall_status": HealthStatus.UNKNOWN.value,
                "health_score": 0.0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

# ============================================================================
# ALERT MANAGER
# ============================================================================

class AlertManager:
    """Alert management system for monitoring events"""

    def __init__(self, max_alerts: int = 1000):
        self.max_alerts = max_alerts
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: deque = deque(maxlen=max_alerts)
        self.alert_handlers: Dict[AlertSeverity, List[Callable]] = defaultdict(list)

        # Alert suppression (prevent spam)
        self.suppression_window = 300  # 5 minutes
        self.suppressed_alerts: Dict[str, datetime] = {}

        logger.info("Alert manager initialized")

    def register_alert_handler(self, severity: AlertSeverity, handler: Callable):
        """Register an alert handler for specific severity"""
        self.alert_handlers[severity].append(handler)
        logger.info(f"Registered alert handler for {severity.value} alerts")

    def create_alert(self, component: str, severity: AlertSeverity, message: str,
                    details: Dict[str, Any] = None) -> str:
        """Create a new alert"""
        try:
            alert_key = f"{component}_{severity.value}_{hash(message)}"

            # Check if alert is suppressed
            if self._is_alert_suppressed(alert_key):
                return ""

            alert_id = str(uuid.uuid4())
            alert = Alert(
                alert_id=alert_id,
                component=component,
                severity=severity,
                message=message,
                details=details or {}
            )

            # Store alert
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)

            # Suppress similar alerts
            self.suppressed_alerts[alert_key] = datetime.now()

            # Trigger alert handlers
            self._trigger_alert_handlers(alert)

            # Log alert
            log_level = {
                AlertSeverity.INFO: logging.INFO,
                AlertSeverity.WARNING: logging.WARNING,
                AlertSeverity.ERROR: logging.ERROR,
                AlertSeverity.CRITICAL: logging.CRITICAL
            }.get(severity, logging.INFO)

            logger.log(log_level, f"Alert created - {component}: {message}")

            return alert_id

        except Exception as e:
            logger.error(f"Failed to create alert: {e}")
            return ""

    def resolve_alert(self, alert_id: str, resolution_message: str = "") -> bool:
        """Resolve an active alert"""
        try:
            if alert_id not in self.active_alerts:
                logger.warning(f"Alert {alert_id} not found")
                return False

            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolved_at = datetime.now()

            if resolution_message:
                alert.details["resolution_message"] = resolution_message

            # Remove from active alerts
            del self.active_alerts[alert_id]

            logger.info(f"Alert resolved - {alert.component}: {alert.message}")
            return True

        except Exception as e:
            logger.error(f"Failed to resolve alert {alert_id}: {e}")
            return False

    def _is_alert_suppressed(self, alert_key: str) -> bool:
        """Check if an alert is currently suppressed"""
        if alert_key not in self.suppressed_alerts:
            return False

        suppressed_at = self.suppressed_alerts[alert_key]
        time_since_suppression = (datetime.now() - suppressed_at).total_seconds()

        if time_since_suppression > self.suppression_window:
            del self.suppressed_alerts[alert_key]
            return False

        return True

    def _trigger_alert_handlers(self, alert: Alert):
        """Trigger registered alert handlers"""
        try:
            handlers = self.alert_handlers.get(alert.severity, [])
            for handler in handlers:
                try:
                    handler(alert)
                except Exception as e:
                    logger.error(f"Alert handler failed: {e}")
        except Exception as e:
            logger.error(f"Failed to trigger alert handlers: {e}")

    def get_active_alerts(self, severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """Get active alerts, optionally filtered by severity"""
        try:
            alerts = list(self.active_alerts.values())

            if severity:
                alerts = [alert for alert in alerts if alert.severity == severity]

            # Sort by timestamp (newest first)
            alerts.sort(key=lambda x: x.timestamp, reverse=True)

            return alerts

        except Exception as e:
            logger.error(f"Failed to get active alerts: {e}")
            return []

    def get_alert_summary(self, hours_back: int = 24) -> Dict[str, Any]:
        """Get alert summary for the specified time period"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)

            # Filter recent alerts
            recent_alerts = [
                alert for alert in self.alert_history
                if alert.timestamp >= cutoff_time
            ]

            # Count by severity
            severity_counts = defaultdict(int)
            for alert in recent_alerts:
                severity_counts[alert.severity.value] += 1

            # Count by component
            component_counts = defaultdict(int)
            for alert in recent_alerts:
                component_counts[alert.component] += 1

            # Resolution statistics
            resolved_alerts = [alert for alert in recent_alerts if alert.resolved]
            resolution_rate = (len(resolved_alerts) / len(recent_alerts) * 100) if recent_alerts else 0

            return {
                "time_period_hours": hours_back,
                "total_alerts": len(recent_alerts),
                "active_alerts": len(self.active_alerts),
                "resolved_alerts": len(resolved_alerts),
                "resolution_rate_percent": round(resolution_rate, 1),
                "severity_breakdown": dict(severity_counts),
                "component_breakdown": dict(component_counts),
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get alert summary: {e}")
            return {"error": str(e)}

# ============================================================================
# MAIN MONITORING SYSTEM
# ============================================================================

class AtlasMonitoringSystem:
    """Main monitoring system coordinating all monitoring components"""

    def __init__(self, prometheus_port: int = 8000):
        self.status = EngineStatus.INITIALIZING
        self.prometheus_port = prometheus_port

        # Initialize components
        self.metrics_collector = PrometheusMetricsCollector()
        self.performance_monitor = PerformanceMonitor()
        self.health_checker = HealthCheckSystem()
        self.alert_manager = AlertManager()

        # Monitoring state
        self.prometheus_server_started = False

        # Register default alert handlers
        self._register_default_alert_handlers()

        logger.info("Atlas monitoring system initialized")

    async def initialize(self) -> bool:
        """Initialize the monitoring system"""
        try:
            # Start Prometheus metrics server
            if PROMETHEUS_AVAILABLE:
                try:
                    start_http_server(self.prometheus_port)
                    self.prometheus_server_started = True
                    logger.info(f"Prometheus metrics server started on port {self.prometheus_port}")
                except Exception as e:
                    logger.warning(f"Failed to start Prometheus server: {e}")

            # Start monitoring components
            self.performance_monitor.start_monitoring()
            self.health_checker.start_health_monitoring()

            self.status = EngineStatus.ACTIVE
            logger.info("Atlas monitoring system initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize monitoring system: {e}")
            self.status = EngineStatus.FAILED
            return False

    def _register_default_alert_handlers(self):
        """Register default alert handlers"""
        # Critical alert handler
        def critical_alert_handler(alert: Alert):
            logger.critical(f"CRITICAL ALERT - {alert.component}: {alert.message}")
            # In production, this could send notifications, emails, etc.

        # Error alert handler
        def error_alert_handler(alert: Alert):
            logger.error(f"ERROR ALERT - {alert.component}: {alert.message}")

        self.alert_manager.register_alert_handler(AlertSeverity.CRITICAL, critical_alert_handler)
        self.alert_manager.register_alert_handler(AlertSeverity.ERROR, error_alert_handler)

    def record_agent_metrics(self, agent_role: str, status: str, duration: float, confidence: float):
        """Record agent performance metrics"""
        self.metrics_collector.record_agent_request(agent_role, status, duration, confidence)

    def record_orchestration_metrics(self, intent: str, mode: str, status: str, duration: float):
        """Record orchestration metrics"""
        self.metrics_collector.record_orchestration_request(intent, mode, status, duration)

    def update_system_metrics(self):
        """Update system resource metrics"""
        try:
            current_metrics = self.performance_monitor.get_current_metrics()

            if "error" not in current_metrics:
                cpu_percent = current_metrics["cpu"]["usage_percent"]
                memory_bytes = current_metrics["memory"]["used_bytes"]

                # Update Prometheus metrics
                self.metrics_collector.update_system_metrics(cpu_percent, memory_bytes, 0)  # TODO: Get actual session count

                # Check for performance alerts
                if cpu_percent > 90:
                    self.alert_manager.create_alert(
                        "system_performance",
                        AlertSeverity.CRITICAL,
                        f"Critical CPU usage: {cpu_percent:.1f}%",
                        {"cpu_percent": cpu_percent}
                    )
                elif cpu_percent > 80:
                    self.alert_manager.create_alert(
                        "system_performance",
                        AlertSeverity.WARNING,
                        f"High CPU usage: {cpu_percent:.1f}%",
                        {"cpu_percent": cpu_percent}
                    )

        except Exception as e:
            logger.error(f"Failed to update system metrics: {e}")

    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive monitoring dashboard data"""
        try:
            # Get current metrics
            current_metrics = self.performance_monitor.get_current_metrics()

            # Get health status
            health_status = self.health_checker.get_overall_health()

            # Get alert summary
            alert_summary = self.alert_manager.get_alert_summary(hours_back=24)

            # Get active alerts
            active_alerts = self.alert_manager.get_active_alerts()

            return {
                "system_status": self.status.value,
                "current_metrics": current_metrics,
                "health_status": health_status,
                "alert_summary": alert_summary,
                "active_alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "component": alert.component,
                        "severity": alert.severity.value,
                        "message": alert.message,
                        "timestamp": alert.timestamp.isoformat()
                    }
                    for alert in active_alerts[:10]  # Show top 10 recent alerts
                ],
                "prometheus_enabled": PROMETHEUS_AVAILABLE and self.prometheus_server_started,
                "prometheus_port": self.prometheus_port if self.prometheus_server_started else None,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get monitoring dashboard: {e}")
            return {"error": str(e)}

    async def shutdown(self):
        """Shutdown the monitoring system"""
        try:
            logger.info("Shutting down monitoring system...")

            # Stop monitoring components
            self.performance_monitor.stop_monitoring()
            self.health_checker.stop_health_monitoring()

            self.status = EngineStatus.STOPPED
            logger.info("Monitoring system shutdown completed")

        except Exception as e:
            logger.error(f"Monitoring system shutdown failed: {e}")

# ============================================================================
# EXPORTS
# ============================================================================

__all__ = [
    # Enums
    "MetricType",
    "HealthStatus",
    "AlertSeverity",

    # Models
    "MetricPoint",
    "HealthCheck",
    "Alert",

    # Components
    "PrometheusMetricsCollector",
    "PerformanceMonitor",
    "HealthCheckSystem",
    "AlertManager",
    "AtlasMonitoringSystem",

    # Constants
    "PROMETHEUS_AVAILABLE"
]
