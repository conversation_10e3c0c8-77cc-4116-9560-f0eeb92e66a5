#!/usr/bin/env python3
"""
🔍 VERIFY LEE METHOD SYSTEM READINESS

This script performs a comprehensive check to ensure the Lee Method Scanner
is properly configured and ready for live operation.
"""

import asyncio
import sys
import os
import importlib.util
from datetime import datetime

sys.path.append(os.path.dirname(__file__))

async def verify_system():
    """Verify all system components are ready"""
    
    print("🔍 LEE METHOD SYSTEM READINESS CHECK")
    print("=" * 50)
    
    checks_passed = 0
    total_checks = 10
    
    # Check 1: Dependencies
    print("1️⃣ Checking dependencies...")
    try:
        import pandas as pd
        import numpy as np
        import requests
        import fastapi
        import uvicorn
        print("   ✅ All required packages installed")
        checks_passed += 1
    except ImportError as e:
        print(f"   ❌ Missing dependency: {e}")
        print("   Run: pip install pandas numpy requests fastapi uvicorn")
    
    # Check 2: API Configuration
    print("\n2️⃣ Checking API configuration...")
    try:
        from config import get_api_config
        fmp_config = get_api_config('fmp')
        fmp_api_key = fmp_config.get('api_key') if fmp_config else None
        
        if fmp_api_key and fmp_api_key not in ['demo', 'test', 'placeholder']:
            print("   ✅ FMP API key configured")
            checks_passed += 1
        else:
            print("   ❌ FMP API key missing or invalid")
            print("   Configure your API key in config.py")
    except Exception as e:
        print(f"   ❌ Configuration error: {e}")
    
    # Check 3: Core modules
    print("\n3️⃣ Checking core modules...")
    try:
        from atlas_lee_method import LeeMethodScanner, AtlasLeeMethodRealtimeScanner
        from atlas_orchestrator import AtlasOrchestrator
        from atlas_server import app
        print("   ✅ All core modules importable")
        checks_passed += 1
    except Exception as e:
        print(f"   ❌ Module import error: {e}")
    
    # Check 4: Scanner initialization
    print("\n4️⃣ Testing scanner initialization...")
    try:
        from atlas_lee_method import AtlasLeeMethodRealtimeScanner
        from config import get_api_config
        
        fmp_config = get_api_config('fmp')
        fmp_api_key = fmp_config.get('api_key') if fmp_config else None
        
        scanner = AtlasLeeMethodRealtimeScanner(fmp_api_key)
        print("   ✅ Realtime scanner initializes correctly")
        checks_passed += 1
    except Exception as e:
        print(f"   ❌ Scanner initialization failed: {e}")
    
    # Check 5: Orchestrator integration
    print("\n5️⃣ Testing orchestrator integration...")
    try:
        from atlas_orchestrator import AtlasOrchestrator
        
        orchestrator = AtlasOrchestrator()
        await orchestrator.initialize()
        
        if 'lee_method' in orchestrator.engines:
            lee_engine = orchestrator.engines['lee_method']
            if hasattr(lee_engine, 'active_signals'):
                print("   ✅ Orchestrator properly integrated with realtime scanner")
                checks_passed += 1
            else:
                print("   ❌ Orchestrator using wrong scanner type")
        else:
            print("   ❌ Lee Method engine not found in orchestrator")
    except Exception as e:
        print(f"   ❌ Orchestrator integration failed: {e}")
    
    # Check 6: Desktop notifications
    print("\n6️⃣ Testing desktop notifications...")
    try:
        import platform
        import subprocess
        
        system = platform.system()
        if system == "Windows":
            # Test PowerShell availability
            result = subprocess.run(['powershell', '-Command', 'echo "test"'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("   ✅ Desktop notifications ready (Windows PowerShell)")
                checks_passed += 1
            else:
                print("   ❌ PowerShell not available for notifications")
        else:
            print(f"   ⚠️  Desktop notifications not tested on {system}")
            checks_passed += 1  # Don't fail for non-Windows systems
    except Exception as e:
        print(f"   ❌ Desktop notification test failed: {e}")
    
    # Check 7: Signal detection
    print("\n7️⃣ Testing signal detection...")
    try:
        from atlas_lee_method import LeeMethodScanner
        from atlas_market_core import AtlasMarketEngine
        from config import get_api_config
        
        fmp_config = get_api_config('fmp')
        fmp_api_key = fmp_config.get('api_key') if fmp_config else None
        
        market_engine = AtlasMarketEngine()
        scanner = LeeMethodScanner(fmp_api_key, market_engine)
        
        # Test with a single symbol
        signal = await scanner.scan_symbol('AAPL')
        if signal:
            print(f"   ✅ Signal detection working (AAPL: {signal.confidence:.1%})")
        else:
            print("   ✅ Signal detection working (no signal for AAPL - normal)")
        checks_passed += 1
    except Exception as e:
        print(f"   ❌ Signal detection failed: {e}")
    
    # Check 8: Port availability
    print("\n8️⃣ Checking port availability...")
    try:
        import socket
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 8002))
        sock.close()
        
        if result != 0:
            print("   ✅ Port 8002 available for server")
            checks_passed += 1
        else:
            print("   ❌ Port 8002 already in use")
            print("   Stop any existing server or use a different port")
    except Exception as e:
        print(f"   ❌ Port check failed: {e}")
    
    # Check 9: File permissions
    print("\n9️⃣ Checking file permissions...")
    try:
        test_file = "test_write_permissions.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("   ✅ File write permissions OK")
        checks_passed += 1
    except Exception as e:
        print(f"   ❌ File permission error: {e}")
    
    # Check 10: System resources
    print("\n🔟 Checking system resources...")
    try:
        import psutil
        
        memory = psutil.virtual_memory()
        cpu_count = psutil.cpu_count()
        
        if memory.available > 1024 * 1024 * 1024:  # 1GB
            print(f"   ✅ Sufficient memory available ({memory.available // (1024**3)}GB)")
            checks_passed += 1
        else:
            print("   ⚠️  Low memory available")
            checks_passed += 1  # Don't fail, just warn
    except ImportError:
        print("   ⚠️  psutil not available, skipping resource check")
        checks_passed += 1
    except Exception as e:
        print(f"   ❌ Resource check failed: {e}")
    
    # Final assessment
    print("\n" + "=" * 50)
    print(f"📊 SYSTEM READINESS: {checks_passed}/{total_checks} checks passed")
    
    if checks_passed >= 8:
        print("🎉 SYSTEM READY FOR OPERATION!")
        print("✅ Lee Method Scanner is properly configured")
        print("✅ Desktop notifications will work")
        print("✅ Web interface will display signals")
        print("\nTo start the system:")
        print("   python start_lee_method_system_fixed.py")
        return True
    elif checks_passed >= 6:
        print("⚠️  SYSTEM PARTIALLY READY")
        print("Some issues detected but system should work")
        print("Review the failed checks above")
        return False
    else:
        print("❌ SYSTEM NOT READY")
        print("Multiple critical issues detected")
        print("Please fix the issues above before starting")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(verify_system())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n👋 Verification cancelled")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        sys.exit(1)
