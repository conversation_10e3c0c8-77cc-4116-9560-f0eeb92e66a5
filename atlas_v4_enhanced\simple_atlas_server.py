#!/usr/bin/env python3
"""
Simple Atlas Server for Testing
Serves the Atlas interface and provides basic API endpoints for testing
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Dict, Any, List
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="A.T.L.A.S. Trading System - Testing Server",
    description="Simple server for testing Atlas V5 Enhanced system",
    version="5.0.0-testing",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global status tracking
system_status = {
    "initialized": True,
    "start_time": datetime.now().isoformat(),
    "api_keys_configured": True,
    "components": {
        "web_interface": "active",
        "api_endpoints": "active",
        "database": "simulated",
        "market_data": "simulated",
        "ai_engine": "simulated",
        "trading_engine": "simulated"
    }
}

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main Atlas interface"""
    try:
        interface_path = os.path.join(os.path.dirname(__file__), "atlas_interface.html")
        if os.path.exists(interface_path):
            with open(interface_path, "r", encoding="utf-8") as f:
                return HTMLResponse(content=f.read())
        else:
            return HTMLResponse(content="""
            <html>
                <head><title>Atlas V5 Testing Server</title></head>
                <body>
                    <h1>Atlas V5 Enhanced Trading System - Testing Mode</h1>
                    <p>Interface file not found. System running in basic testing mode.</p>
                    <p><a href="/docs">API Documentation</a></p>
                    <p><a href="/api/v1/health">Health Check</a></p>
                    <p><a href="/api/v1/system/status">System Status</a></p>
                </body>
            </html>
            """)
    except Exception as e:
        logger.error(f"Error serving root: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/atlas_interface.html", response_class=HTMLResponse)
async def get_interface():
    """Serve the Atlas interface directly"""
    try:
        interface_path = os.path.join(os.path.dirname(__file__), "atlas_interface.html")
        if os.path.exists(interface_path):
            with open(interface_path, "r", encoding="utf-8") as f:
                return HTMLResponse(content=f.read())
        else:
            raise HTTPException(status_code=404, detail="Interface file not found")
    except Exception as e:
        logger.error(f"Error serving interface: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    return JSONResponse(content={
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "5.0.0-testing",
        "uptime_seconds": (datetime.now() - datetime.fromisoformat(system_status["start_time"])).total_seconds()
    })

@app.get("/api/v1/system/status")
async def system_status_endpoint():
    """System status endpoint"""
    return JSONResponse(content=system_status)

@app.get("/api/v1/system/initialization")
async def initialization_status():
    """Initialization status endpoint"""
    return JSONResponse(content={
        "status": "complete",
        "initialization_percentage": 100,
        "components": system_status["components"],
        "timestamp": datetime.now().isoformat(),
        "system_ready": True,
        "api_endpoints_active": True,
        "database_connected": True,
        "environment": "testing"
    })

@app.post("/api/v1/chat/message")
async def chat_message(request: Request):
    """Simulated chat endpoint"""
    try:
        data = await request.json()
        message = data.get("message", "")
        
        # Simulate AI response
        response = {
            "response": f"[TESTING MODE] Received your message: '{message}'. This is a simulated response for testing the Atlas V5 Enhanced system interface.",
            "timestamp": datetime.now().isoformat(),
            "mode": "testing",
            "features_available": [
                "Web Interface",
                "API Endpoints", 
                "Health Monitoring",
                "System Status"
            ]
        }
        
        return JSONResponse(content=response)
    except Exception as e:
        logger.error(f"Chat endpoint error: {e}")
        return JSONResponse(content={"error": str(e)}, status_code=500)

@app.get("/api/v1/lee_method/signals")
async def lee_method_signals():
    """Simulated Lee Method signals endpoint"""
    return JSONResponse(content={
        "signals": [
            {
                "symbol": "AAPL",
                "signal_strength": 4,
                "criteria_met": ["volume", "price_action", "momentum", "trend"],
                "timestamp": datetime.now().isoformat(),
                "status": "simulated"
            },
            {
                "symbol": "TSLA", 
                "signal_strength": 5,
                "criteria_met": ["volume", "price_action", "momentum", "trend", "breakout"],
                "timestamp": datetime.now().isoformat(),
                "status": "simulated"
            }
        ],
        "total_signals": 2,
        "scan_time": datetime.now().isoformat(),
        "mode": "testing"
    })

@app.get("/api/v1/market/quote/{symbol}")
async def market_quote(symbol: str):
    """Simulated market quote endpoint"""
    return JSONResponse(content={
        "symbol": symbol.upper(),
        "price": 150.00,
        "change": 2.50,
        "change_percent": 1.69,
        "volume": 1000000,
        "timestamp": datetime.now().isoformat(),
        "status": "simulated"
    })

@app.get("/api/v1/scanner/active")
async def active_scanner():
    """Simulated active scanner endpoint"""
    return JSONResponse(content={
        "active_scans": 253,
        "signals_found": 12,
        "last_scan": datetime.now().isoformat(),
        "status": "simulated",
        "performance": {
            "scan_time_seconds": 4.2,
            "symbols_per_second": 60.2
        }
    })

if __name__ == "__main__":
    print("🚀 Starting Atlas V5 Enhanced Testing Server...")
    print("✅ API keys configured (testing mode)")
    print("✅ Web interface ready")
    print("✅ API endpoints active")
    print("🌐 Web interface: http://localhost:8002")
    print("📊 API documentation: http://localhost:8002/docs")
    print("🔍 Health check: http://localhost:8002/api/v1/health")
    
    uvicorn.run(
        "simple_atlas_server:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
        log_level="info"
    )
