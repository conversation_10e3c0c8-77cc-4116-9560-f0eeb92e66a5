#!/usr/bin/env python3
"""
A.T.L.A.S. Consolidated System Startup Script
Starts the consolidated Atlas trading system with all components
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the core directory to Python path
current_dir = Path(__file__).parent
core_dir = current_dir / "core"
sys.path.insert(0, str(core_dir))
sys.path.insert(0, str(current_dir))

# Add all module directories to path
for module_dir in ["trading", "market", "ai", "data", "utils"]:
    sys.path.insert(0, str(current_dir / module_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('atlas_consolidated.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main startup function"""
    try:
        logger.info("🚀 Starting A.T.L.A.S. Consolidated Trading System...")
        
        # Change to core directory for server startup
        os.chdir(core_dir)
        
        # Import and start the server
        logger.info("📡 Starting Atlas server...")
        
        # Import the server module
        import atlas_server
        
        logger.info("✅ A.T.L.A.S. Consolidated System started successfully!")
        logger.info("🌐 Web interface available at: http://localhost:8000")
        logger.info("📊 API documentation at: http://localhost:8000/docs")
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("Make sure all required dependencies are installed")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
