"""
A.T.L.A.S. Real-Time Alert Engine
Advanced alert system for instant Lee Method signal notifications
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json
import uuid
from collections import defaultdict, deque
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
import os
from concurrent.futures import ThreadPoolExecutor

try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertPriority(Enum):
    """Alert priority levels"""
    CRITICAL = "critical"      # Active decline reversal (3+ bars, confidence ≥0.75)
    HIGH = "high"             # Moderate decline (2 bars, confidence ≥0.65)
    MEDIUM = "medium"         # Bullish momentum, other patterns
    LOW = "low"               # Informational alerts

class AlertChannel(Enum):
    """Available alert delivery channels"""
    DESKTOP = "desktop"
    EMAIL = "email"
    WEBSOCKET = "websocket"
    SMS = "sms"
    WEBHOOK = "webhook"

class SignalType(Enum):
    """Lee Method signal types"""
    ACTIVE_DECLINE_REVERSAL = "active_decline_reversal_opportunity"
    MODERATE_DECLINE = "moderate_decline_opportunity"
    BULLISH_MOMENTUM = "bullish_momentum"
    BEARISH_MOMENTUM = "bearish_momentum"
    TTM_SQUEEZE_RELEASE = "ttm_squeeze_release"

@dataclass
class AlertSignal:
    """Represents a Lee Method signal for alerting"""
    id: str
    symbol: str
    signal_type: SignalType
    priority: AlertPriority
    confidence: float
    timeframe: str
    current_price: float
    percentage_change: float
    ttm_squeeze_status: str
    consecutive_bars: int
    timestamp: datetime
    scanner_tier: str
    additional_data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['signal_type'] = self.signal_type.value
        data['priority'] = self.priority.value
        return data

@dataclass
class AlertRule:
    """Custom alert rule configuration"""
    id: str
    name: str
    symbols: List[str]  # Empty list means all symbols
    signal_types: List[SignalType]
    min_confidence: float
    priority_override: Optional[AlertPriority]
    channels: List[AlertChannel]
    enabled: bool
    created_at: datetime

class AlertEngine:
    """
    Core alert engine for real-time Lee Method signal notifications
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.AlertEngine")
        
        # Alert management
        self.active_alerts: Dict[str, AlertSignal] = {}
        self.alert_history: deque = deque(maxlen=10000)  # Keep last 10k alerts
        self.alert_rules: Dict[str, AlertRule] = {}
        self.snoozed_alerts: Dict[str, datetime] = {}
        
        # Channel handlers
        self.channel_handlers: Dict[AlertChannel, Callable] = {}
        self.websocket_connections: set = set()
        
        # Performance tracking
        self.alert_stats = {
            'total_alerts': 0,
            'alerts_by_priority': defaultdict(int),
            'alerts_by_signal_type': defaultdict(int),
            'delivery_success_rate': defaultdict(float),
            'last_reset': datetime.now()
        }
        
        # Configuration
        self.config = {
            'max_alerts_per_minute': 100,
            'duplicate_alert_window': 300,  # 5 minutes
            'snooze_duration': 1800,  # 30 minutes
            'email_enabled': True,
            'desktop_enabled': True,
            'sms_enabled': False,
            'webhook_enabled': False
        }
        
        # Rate limiting
        self.alert_timestamps: deque = deque(maxlen=1000)
        
        # Initialize default rules
        self._initialize_default_rules()
        
        self.logger.info("[ALERT_ENGINE] Real-time alert engine initialized")
    
    def _initialize_default_rules(self):
        """Initialize default alert rules"""
        # High priority reversal opportunities
        high_priority_rule = AlertRule(
            id="default_high_priority",
            name="High Priority Reversals",
            symbols=[],  # All symbols
            signal_types=[SignalType.ACTIVE_DECLINE_REVERSAL],
            min_confidence=0.75,
            priority_override=AlertPriority.CRITICAL,
            channels=[AlertChannel.WEBSOCKET, AlertChannel.DESKTOP],  # WebSocket first for in-app notifications
            enabled=True,
            created_at=datetime.now()
        )
        
        # Medium priority opportunities
        medium_priority_rule = AlertRule(
            id="default_medium_priority",
            name="Medium Priority Opportunities",
            symbols=[],
            signal_types=[SignalType.MODERATE_DECLINE],
            min_confidence=0.65,
            priority_override=AlertPriority.HIGH,
            channels=[AlertChannel.WEBSOCKET, AlertChannel.DESKTOP],  # WebSocket first for in-app notifications
            enabled=True,
            created_at=datetime.now()
        )
        
        # Bullish momentum
        bullish_rule = AlertRule(
            id="default_bullish",
            name="Bullish Momentum Signals",
            symbols=[],
            signal_types=[SignalType.BULLISH_MOMENTUM],
            min_confidence=0.60,
            priority_override=AlertPriority.MEDIUM,
            channels=[AlertChannel.WEBSOCKET],
            enabled=True,
            created_at=datetime.now()
        )
        
        self.alert_rules.update({
            "default_high_priority": high_priority_rule,
            "default_medium_priority": medium_priority_rule,
            "default_bullish": bullish_rule
        })
    
    async def process_lee_method_signal(self, signal_data: Dict[str, Any]) -> Optional[AlertSignal]:
        """
        Process incoming Lee Method signal and determine if alert should be triggered
        """
        try:
            # Extract signal information
            symbol = signal_data.get('symbol', '')
            signal_type_str = signal_data.get('signal_type', '')
            confidence = float(signal_data.get('confidence', 0.0))
            timeframe = signal_data.get('timeframe', '1day')
            current_price = float(signal_data.get('current_price', 0.0))
            percentage_change = float(signal_data.get('percentage_change', 0.0))
            ttm_squeeze_status = signal_data.get('ttm_squeeze_status', 'unknown')
            consecutive_bars = int(signal_data.get('consecutive_bars', 0))
            scanner_tier = signal_data.get('scanner_tier', 'unknown')
            
            # Map signal type
            signal_type = self._map_signal_type(signal_type_str)
            if not signal_type:
                return None
            
            # Determine priority
            priority = self._determine_priority(signal_type, confidence, consecutive_bars)
            
            # Create alert signal
            alert_signal = AlertSignal(
                id=str(uuid.uuid4()),
                symbol=symbol,
                signal_type=signal_type,
                priority=priority,
                confidence=confidence,
                timeframe=timeframe,
                current_price=current_price,
                percentage_change=percentage_change,
                ttm_squeeze_status=ttm_squeeze_status,
                consecutive_bars=consecutive_bars,
                timestamp=datetime.now(),
                scanner_tier=scanner_tier,
                additional_data=signal_data
            )
            
            # Check if alert should be triggered
            if await self._should_trigger_alert(alert_signal):
                await self._trigger_alert(alert_signal)
                return alert_signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"[ALERT_ENGINE] Error processing signal: {e}")
            return None
    
    def _map_signal_type(self, signal_type_str: str) -> Optional[SignalType]:
        """Map string signal type to enum"""
        mapping = {
            'active_decline_reversal_opportunity': SignalType.ACTIVE_DECLINE_REVERSAL,
            'moderate_decline_opportunity': SignalType.MODERATE_DECLINE,
            'bullish_momentum': SignalType.BULLISH_MOMENTUM,
            'bearish_momentum': SignalType.BEARISH_MOMENTUM,
            'ttm_squeeze_release': SignalType.TTM_SQUEEZE_RELEASE
        }
        return mapping.get(signal_type_str.lower())
    
    def _determine_priority(self, signal_type: SignalType, confidence: float, consecutive_bars: int) -> AlertPriority:
        """Determine alert priority based on signal characteristics"""
        if signal_type == SignalType.ACTIVE_DECLINE_REVERSAL and confidence >= 0.75:
            return AlertPriority.CRITICAL
        elif signal_type == SignalType.MODERATE_DECLINE and confidence >= 0.65:
            return AlertPriority.HIGH
        elif signal_type == SignalType.BULLISH_MOMENTUM and confidence >= 0.70:
            return AlertPriority.MEDIUM
        else:
            return AlertPriority.LOW
    
    async def _should_trigger_alert(self, alert_signal: AlertSignal) -> bool:
        """Determine if alert should be triggered based on rules and rate limiting"""
        # Check rate limiting
        now = datetime.now()
        minute_ago = now - timedelta(minutes=1)
        recent_alerts = [ts for ts in self.alert_timestamps if ts > minute_ago]
        
        if len(recent_alerts) >= self.config['max_alerts_per_minute']:
            self.logger.warning(f"[ALERT_ENGINE] Rate limit exceeded, skipping alert for {alert_signal.symbol}")
            return False
        
        # Check for duplicate alerts
        duplicate_window = now - timedelta(seconds=self.config['duplicate_alert_window'])
        for existing_alert in self.active_alerts.values():
            if (existing_alert.symbol == alert_signal.symbol and 
                existing_alert.signal_type == alert_signal.signal_type and
                existing_alert.timestamp > duplicate_window):
                self.logger.debug(f"[ALERT_ENGINE] Duplicate alert suppressed for {alert_signal.symbol}")
                return False
        
        # Check snooze status
        snooze_key = f"{alert_signal.symbol}_{alert_signal.signal_type.value}"
        if snooze_key in self.snoozed_alerts:
            if now < self.snoozed_alerts[snooze_key]:
                return False
            else:
                del self.snoozed_alerts[snooze_key]
        
        # Check custom rules
        matching_rules = self._get_matching_rules(alert_signal)
        if not matching_rules:
            return False
        
        return True
    
    def _get_matching_rules(self, alert_signal: AlertSignal) -> List[AlertRule]:
        """Get alert rules that match the signal"""
        matching_rules = []
        
        for rule in self.alert_rules.values():
            if not rule.enabled:
                continue
            
            # Check symbol filter
            if rule.symbols and alert_signal.symbol not in rule.symbols:
                continue
            
            # Check signal type filter
            if rule.signal_types and alert_signal.signal_type not in rule.signal_types:
                continue
            
            # Check minimum confidence
            if alert_signal.confidence < rule.min_confidence:
                continue
            
            matching_rules.append(rule)
        
        return matching_rules
    
    async def _trigger_alert(self, alert_signal: AlertSignal):
        """Trigger alert through all configured channels"""
        try:
            # Add to active alerts
            self.active_alerts[alert_signal.id] = alert_signal
            self.alert_history.append(alert_signal)
            self.alert_timestamps.append(alert_signal.timestamp)
            
            # Update statistics
            self.alert_stats['total_alerts'] += 1
            self.alert_stats['alerts_by_priority'][alert_signal.priority.value] += 1
            self.alert_stats['alerts_by_signal_type'][alert_signal.signal_type.value] += 1
            
            # Get matching rules to determine channels
            matching_rules = self._get_matching_rules(alert_signal)
            channels_to_use = set()
            
            for rule in matching_rules:
                channels_to_use.update(rule.channels)
            
            # Send alerts through each channel
            delivery_tasks = []
            for channel in channels_to_use:
                if channel in self.channel_handlers:
                    task = asyncio.create_task(
                        self._deliver_alert(alert_signal, channel)
                    )
                    delivery_tasks.append(task)
            
            # Wait for all deliveries
            if delivery_tasks:
                await asyncio.gather(*delivery_tasks, return_exceptions=True)
            
            self.logger.info(f"[ALERT_ENGINE] Alert triggered for {alert_signal.symbol}: {alert_signal.signal_type.value}")
            
        except Exception as e:
            self.logger.error(f"[ALERT_ENGINE] Error triggering alert: {e}")
    
    async def _deliver_alert(self, alert_signal: AlertSignal, channel: AlertChannel):
        """Deliver alert through specific channel"""
        try:
            handler = self.channel_handlers.get(channel)
            if handler:
                await handler(alert_signal)
                self.alert_stats['delivery_success_rate'][channel.value] += 1
            else:
                self.logger.warning(f"[ALERT_ENGINE] No handler for channel: {channel.value}")
        except Exception as e:
            self.logger.error(f"[ALERT_ENGINE] Error delivering alert via {channel.value}: {e}")
    
    def register_channel_handler(self, channel: AlertChannel, handler: Callable):
        """Register a handler for a specific alert channel"""
        self.channel_handlers[channel] = handler
        self.logger.info(f"[ALERT_ENGINE] Registered handler for {channel.value}")
    
    async def add_websocket_connection(self, websocket):
        """Add WebSocket connection for real-time alerts"""
        self.websocket_connections.add(websocket)
        self.logger.info(f"[ALERT_ENGINE] WebSocket connection added: {len(self.websocket_connections)} total")
    
    async def remove_websocket_connection(self, websocket):
        """Remove WebSocket connection"""
        self.websocket_connections.discard(websocket)
        self.logger.info(f"[ALERT_ENGINE] WebSocket connection removed: {len(self.websocket_connections)} total")
    
    def snooze_alert(self, alert_id: str, duration_minutes: int = 30):
        """Snooze alerts for a specific symbol/signal type"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            snooze_key = f"{alert.symbol}_{alert.signal_type.value}"
            snooze_until = datetime.now() + timedelta(minutes=duration_minutes)
            self.snoozed_alerts[snooze_key] = snooze_until
            self.logger.info(f"[ALERT_ENGINE] Alert snoozed for {alert.symbol} until {snooze_until}")
    
    def dismiss_alert(self, alert_id: str):
        """Dismiss an active alert"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts.pop(alert_id)
            self.logger.info(f"[ALERT_ENGINE] Alert dismissed for {alert.symbol}")
    
    def get_active_alerts(self) -> List[AlertSignal]:
        """Get all active alerts"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[AlertSignal]:
        """Get recent alert history"""
        return list(self.alert_history)[-limit:]
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """Get alert statistics"""
        return dict(self.alert_stats)
    
    async def cleanup_old_alerts(self):
        """Clean up old alerts and maintain system performance"""
        now = datetime.now()
        cutoff_time = now - timedelta(hours=24)  # Keep alerts for 24 hours
        
        # Remove old active alerts
        old_alert_ids = [
            alert_id for alert_id, alert in self.active_alerts.items()
            if alert.timestamp < cutoff_time
        ]
        
        for alert_id in old_alert_ids:
            del self.active_alerts[alert_id]
        
        # Clean up old snooze entries
        expired_snoozes = [
            key for key, snooze_time in self.snoozed_alerts.items()
            if now > snooze_time
        ]
        
        for key in expired_snoozes:
            del self.snoozed_alerts[key]
        
        if old_alert_ids or expired_snoozes:
            self.logger.info(f"[ALERT_ENGINE] Cleaned up {len(old_alert_ids)} old alerts and {len(expired_snoozes)} expired snoozes")

# Global alert engine instance
alert_engine = AlertEngine()
