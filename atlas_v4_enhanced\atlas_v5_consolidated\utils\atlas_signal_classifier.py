"""
A.T.L.A.S. Signal Classification System
Intelligent classification of Lee Method signals based on multiple criteria
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import json
import numpy as np
from atlas_alert_engine import AlertSignal, AlertPriority, SignalType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketCondition(Enum):
    """Market condition classifications"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"

class SectorStrength(Enum):
    """Sector strength classifications"""
    STRONG = "strong"
    MODERATE = "moderate"
    WEAK = "weak"
    UNKNOWN = "unknown"

@dataclass
class SignalContext:
    """Additional context for signal classification"""
    market_condition: MarketCondition
    sector_strength: SectorStrength
    volume_profile: str
    recent_news_sentiment: float
    technical_confluence: int
    risk_reward_ratio: float
    support_resistance_proximity: float

@dataclass
class ClassificationRule:
    """Rule for signal classification"""
    name: str
    conditions: Dict[str, Any]
    priority_adjustment: int  # -2 to +2
    confidence_multiplier: float  # 0.5 to 1.5
    enabled: bool
    weight: float

class SignalClassifier:
    """
    Advanced signal classifier that analyzes Lee Method signals
    and determines their priority and reliability
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.SignalClassifier")
        
        # Classification rules
        self.classification_rules: List[ClassificationRule] = []
        
        # Market context cache
        self.market_context_cache: Dict[str, Any] = {}
        self.context_cache_expiry = timedelta(minutes=5)
        self.last_context_update = datetime.min
        
        # Signal history for pattern recognition
        self.signal_history: List[AlertSignal] = []
        self.max_history_size = 1000
        
        # Performance tracking
        self.classification_stats = {
            'total_classified': 0,
            'priority_upgrades': 0,
            'priority_downgrades': 0,
            'confidence_adjustments': 0,
            'context_hits': 0
        }
        
        # Initialize default rules
        self._initialize_classification_rules()
        
        self.logger.info("[CLASSIFIER] Signal classifier initialized")
    
    def _initialize_classification_rules(self):
        """Initialize default classification rules"""
        
        # High confidence reversal rule
        high_confidence_reversal = ClassificationRule(
            name="high_confidence_reversal",
            conditions={
                'signal_type': [SignalType.ACTIVE_DECLINE_REVERSAL],
                'min_confidence': 0.80,
                'min_consecutive_bars': 3,
                'ttm_squeeze_status': ['squeeze_released']
            },
            priority_adjustment=1,
            confidence_multiplier=1.2,
            enabled=True,
            weight=1.0
        )
        
        # TTM Squeeze release rule
        squeeze_release_rule = ClassificationRule(
            name="ttm_squeeze_release",
            conditions={
                'ttm_squeeze_status': ['squeeze_released'],
                'min_confidence': 0.65
            },
            priority_adjustment=1,
            confidence_multiplier=1.15,
            enabled=True,
            weight=0.8
        )
        
        # Multiple timeframe confirmation
        multi_timeframe_rule = ClassificationRule(
            name="multi_timeframe_confirmation",
            conditions={
                'timeframe_confirmation': True,
                'min_confidence': 0.70
            },
            priority_adjustment=1,
            confidence_multiplier=1.25,
            enabled=True,
            weight=1.2
        )
        
        # High volume confirmation
        volume_confirmation_rule = ClassificationRule(
            name="volume_confirmation",
            conditions={
                'volume_profile': ['high', 'above_average'],
                'min_confidence': 0.60
            },
            priority_adjustment=0,
            confidence_multiplier=1.1,
            enabled=True,
            weight=0.7
        )
        
        # Market condition alignment
        market_alignment_rule = ClassificationRule(
            name="market_alignment",
            conditions={
                'market_condition_alignment': True,
                'min_confidence': 0.65
            },
            priority_adjustment=0,
            confidence_multiplier=1.1,
            enabled=True,
            weight=0.9
        )
        
        # Low confidence downgrade
        low_confidence_rule = ClassificationRule(
            name="low_confidence_downgrade",
            conditions={
                'max_confidence': 0.55
            },
            priority_adjustment=-1,
            confidence_multiplier=0.9,
            enabled=True,
            weight=1.0
        )
        
        # Add rules to list
        self.classification_rules.extend([
            high_confidence_reversal,
            squeeze_release_rule,
            multi_timeframe_rule,
            volume_confirmation_rule,
            market_alignment_rule,
            low_confidence_rule
        ])
    
    async def classify_signal(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]] = None) -> Tuple[AlertSignal, SignalContext]:
        """
        Classify and potentially modify signal based on advanced criteria
        """
        try:
            # Get market context
            context = await self._get_signal_context(alert_signal, additional_data)
            
            # Apply classification rules
            modified_signal = await self._apply_classification_rules(alert_signal, context)
            
            # Add to signal history
            self._add_to_history(modified_signal)
            
            # Update statistics
            self._update_classification_stats(alert_signal, modified_signal)
            
            self.logger.debug(f"[CLASSIFIER] Classified signal for {alert_signal.symbol}: {alert_signal.priority.value} -> {modified_signal.priority.value}")
            
            return modified_signal, context
            
        except Exception as e:
            self.logger.error(f"[CLASSIFIER] Error classifying signal: {e}")
            return alert_signal, SignalContext(
                market_condition=MarketCondition.UNKNOWN,
                sector_strength=SectorStrength.UNKNOWN,
                volume_profile="unknown",
                recent_news_sentiment=0.0,
                technical_confluence=0,
                risk_reward_ratio=1.0,
                support_resistance_proximity=0.0
            )
    
    async def _get_signal_context(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> SignalContext:
        """Get contextual information for signal classification"""
        
        # Check cache first
        if self._is_context_cache_valid():
            cached_context = self.market_context_cache.get(alert_signal.symbol)
            if cached_context:
                return SignalContext(**cached_context)
        
        # Build context from available data
        context = SignalContext(
            market_condition=self._determine_market_condition(alert_signal, additional_data),
            sector_strength=self._determine_sector_strength(alert_signal, additional_data),
            volume_profile=self._analyze_volume_profile(alert_signal, additional_data),
            recent_news_sentiment=self._get_news_sentiment(alert_signal, additional_data),
            technical_confluence=self._calculate_technical_confluence(alert_signal, additional_data),
            risk_reward_ratio=self._calculate_risk_reward_ratio(alert_signal, additional_data),
            support_resistance_proximity=self._calculate_support_resistance_proximity(alert_signal, additional_data)
        )
        
        # Cache the context
        self.market_context_cache[alert_signal.symbol] = {
            'market_condition': context.market_condition,
            'sector_strength': context.sector_strength,
            'volume_profile': context.volume_profile,
            'recent_news_sentiment': context.recent_news_sentiment,
            'technical_confluence': context.technical_confluence,
            'risk_reward_ratio': context.risk_reward_ratio,
            'support_resistance_proximity': context.support_resistance_proximity
        }
        
        return context
    
    def _is_context_cache_valid(self) -> bool:
        """Check if context cache is still valid"""
        return datetime.now() - self.last_context_update < self.context_cache_expiry
    
    def _determine_market_condition(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> MarketCondition:
        """Determine overall market condition"""
        if not additional_data:
            return MarketCondition.UNKNOWN
        
        # Look for market indicators in additional data
        market_data = additional_data.get('market_condition', {})
        
        if 'spy_trend' in market_data:
            spy_trend = market_data['spy_trend']
            if spy_trend > 0.02:  # 2% positive trend
                return MarketCondition.BULLISH
            elif spy_trend < -0.02:  # 2% negative trend
                return MarketCondition.BEARISH
            else:
                return MarketCondition.SIDEWAYS
        
        # Fallback: analyze from signal characteristics
        if alert_signal.signal_type == SignalType.BULLISH_MOMENTUM:
            return MarketCondition.BULLISH
        elif alert_signal.signal_type == SignalType.BEARISH_MOMENTUM:
            return MarketCondition.BEARISH
        
        return MarketCondition.UNKNOWN
    
    def _determine_sector_strength(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> SectorStrength:
        """Determine sector strength"""
        if not additional_data:
            return SectorStrength.UNKNOWN
        
        sector_data = additional_data.get('sector_analysis', {})
        sector_performance = sector_data.get('relative_strength', 0.0)
        
        if sector_performance > 0.05:  # 5% outperformance
            return SectorStrength.STRONG
        elif sector_performance > -0.05:  # Within 5%
            return SectorStrength.MODERATE
        else:
            return SectorStrength.WEAK
    
    def _analyze_volume_profile(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> str:
        """Analyze volume profile"""
        if not additional_data:
            return "unknown"
        
        volume_data = additional_data.get('volume_analysis', {})
        relative_volume = volume_data.get('relative_volume', 1.0)
        
        if relative_volume > 2.0:
            return "high"
        elif relative_volume > 1.5:
            return "above_average"
        elif relative_volume > 0.5:
            return "average"
        else:
            return "low"
    
    def _get_news_sentiment(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> float:
        """Get recent news sentiment score"""
        if not additional_data:
            return 0.0
        
        news_data = additional_data.get('news_sentiment', {})
        return news_data.get('sentiment_score', 0.0)
    
    def _calculate_technical_confluence(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> int:
        """Calculate technical confluence score"""
        confluence_score = 0
        
        # Base signal gets 1 point
        confluence_score += 1
        
        # TTM Squeeze status
        if alert_signal.ttm_squeeze_status == "squeeze_released":
            confluence_score += 2
        elif alert_signal.ttm_squeeze_status == "squeeze_active":
            confluence_score += 1
        
        # Confidence level
        if alert_signal.confidence >= 0.80:
            confluence_score += 2
        elif alert_signal.confidence >= 0.70:
            confluence_score += 1
        
        # Consecutive bars
        if alert_signal.consecutive_bars >= 3:
            confluence_score += 1
        
        # Additional technical indicators from additional_data
        if additional_data:
            technical_data = additional_data.get('technical_indicators', {})
            
            # RSI divergence
            if technical_data.get('rsi_divergence', False):
                confluence_score += 1
            
            # MACD confirmation
            if technical_data.get('macd_confirmation', False):
                confluence_score += 1
            
            # Support/Resistance level
            if technical_data.get('at_key_level', False):
                confluence_score += 1
        
        return confluence_score
    
    def _calculate_risk_reward_ratio(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> float:
        """Calculate risk/reward ratio"""
        if not additional_data:
            return 1.0
        
        risk_data = additional_data.get('risk_analysis', {})
        return risk_data.get('risk_reward_ratio', 1.0)
    
    def _calculate_support_resistance_proximity(self, alert_signal: AlertSignal, additional_data: Optional[Dict[str, Any]]) -> float:
        """Calculate proximity to support/resistance levels"""
        if not additional_data:
            return 0.0
        
        levels_data = additional_data.get('support_resistance', {})
        return levels_data.get('proximity_score', 0.0)
    
    async def _apply_classification_rules(self, alert_signal: AlertSignal, context: SignalContext) -> AlertSignal:
        """Apply classification rules to modify signal priority and confidence"""
        
        modified_signal = AlertSignal(
            id=alert_signal.id,
            symbol=alert_signal.symbol,
            signal_type=alert_signal.signal_type,
            priority=alert_signal.priority,
            confidence=alert_signal.confidence,
            timeframe=alert_signal.timeframe,
            current_price=alert_signal.current_price,
            percentage_change=alert_signal.percentage_change,
            ttm_squeeze_status=alert_signal.ttm_squeeze_status,
            consecutive_bars=alert_signal.consecutive_bars,
            timestamp=alert_signal.timestamp,
            scanner_tier=alert_signal.scanner_tier,
            additional_data=alert_signal.additional_data
        )
        
        total_priority_adjustment = 0
        total_confidence_multiplier = 1.0
        total_weight = 0.0
        
        # Apply each rule
        for rule in self.classification_rules:
            if not rule.enabled:
                continue
            
            if self._rule_matches(rule, modified_signal, context):
                total_priority_adjustment += rule.priority_adjustment * rule.weight
                total_confidence_multiplier += (rule.confidence_multiplier - 1.0) * rule.weight
                total_weight += rule.weight
        
        # Normalize adjustments
        if total_weight > 0:
            avg_priority_adjustment = total_priority_adjustment / total_weight
            avg_confidence_multiplier = total_confidence_multiplier / total_weight
        else:
            avg_priority_adjustment = 0
            avg_confidence_multiplier = 1.0
        
        # Apply priority adjustment
        current_priority_value = list(AlertPriority).index(modified_signal.priority)
        new_priority_value = max(0, min(len(AlertPriority) - 1, current_priority_value + int(avg_priority_adjustment)))
        modified_signal.priority = list(AlertPriority)[new_priority_value]
        
        # Apply confidence adjustment
        modified_signal.confidence = min(1.0, max(0.0, modified_signal.confidence * avg_confidence_multiplier))
        
        return modified_signal
    
    def _rule_matches(self, rule: ClassificationRule, signal: AlertSignal, context: SignalContext) -> bool:
        """Check if a rule matches the given signal and context"""
        conditions = rule.conditions
        
        # Check signal type
        if 'signal_type' in conditions:
            if signal.signal_type not in conditions['signal_type']:
                return False
        
        # Check confidence thresholds
        if 'min_confidence' in conditions:
            if signal.confidence < conditions['min_confidence']:
                return False
        
        if 'max_confidence' in conditions:
            if signal.confidence > conditions['max_confidence']:
                return False
        
        # Check consecutive bars
        if 'min_consecutive_bars' in conditions:
            if signal.consecutive_bars < conditions['min_consecutive_bars']:
                return False
        
        # Check TTM Squeeze status
        if 'ttm_squeeze_status' in conditions:
            if signal.ttm_squeeze_status not in conditions['ttm_squeeze_status']:
                return False
        
        # Check volume profile
        if 'volume_profile' in conditions:
            if context.volume_profile not in conditions['volume_profile']:
                return False
        
        # Check market condition alignment
        if 'market_condition_alignment' in conditions:
            if conditions['market_condition_alignment']:
                # Check if signal aligns with market condition
                if signal.signal_type == SignalType.BULLISH_MOMENTUM and context.market_condition != MarketCondition.BULLISH:
                    return False
                if signal.signal_type == SignalType.BEARISH_MOMENTUM and context.market_condition != MarketCondition.BEARISH:
                    return False
        
        # Check timeframe confirmation
        if 'timeframe_confirmation' in conditions:
            if conditions['timeframe_confirmation']:
                # This would require checking multiple timeframes
                # For now, assume it's confirmed if we have the data
                return 'multi_timeframe_data' in signal.additional_data
        
        return True
    
    def _add_to_history(self, signal: AlertSignal):
        """Add signal to history for pattern recognition"""
        self.signal_history.append(signal)
        
        # Maintain history size
        if len(self.signal_history) > self.max_history_size:
            self.signal_history = self.signal_history[-self.max_history_size:]
    
    def _update_classification_stats(self, original: AlertSignal, modified: AlertSignal):
        """Update classification statistics"""
        self.classification_stats['total_classified'] += 1
        
        if modified.priority != original.priority:
            original_priority_value = list(AlertPriority).index(original.priority)
            modified_priority_value = list(AlertPriority).index(modified.priority)
            
            if modified_priority_value > original_priority_value:
                self.classification_stats['priority_upgrades'] += 1
            else:
                self.classification_stats['priority_downgrades'] += 1
        
        if abs(modified.confidence - original.confidence) > 0.01:
            self.classification_stats['confidence_adjustments'] += 1
    
    def get_classification_stats(self) -> Dict[str, Any]:
        """Get classification statistics"""
        return dict(self.classification_stats)
    
    def add_custom_rule(self, rule: ClassificationRule):
        """Add a custom classification rule"""
        self.classification_rules.append(rule)
        self.logger.info(f"[CLASSIFIER] Added custom rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """Remove a classification rule"""
        self.classification_rules = [r for r in self.classification_rules if r.name != rule_name]
        self.logger.info(f"[CLASSIFIER] Removed rule: {rule_name}")
    
    def get_rules(self) -> List[ClassificationRule]:
        """Get all classification rules"""
        return self.classification_rules.copy()

# Global classifier instance
signal_classifier = SignalClassifier()
